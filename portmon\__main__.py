#!/usr/bin/env python3
# File: portmon/__main__.py
# Description: Entry point for running portmon as a module

"""
Portmon - Port Monitoring Tool

Provides continuous monitoring of open ports and services on multiple targets.
"""

import sys
import logging
from colorama import Fore, Style

# Import the main function from our main module
from .main import main
from .utils import setup_logging

# Basic logging setup for package execution
setup_logging()
logger = logging.getLogger(__name__)

# Entry point when run as a module
if __name__ == '__main__':
    try:
        logger.info(f"{Fore.BLUE}Starting Portmon{Style.RESET_ALL} 🚀")
        sys.exit(main())
    except KeyboardInterrupt:
        logger.info(f"\n{Fore.YELLOW}Portmon execution interrupted.{Style.RESET_ALL} 👋")
        sys.exit(130)  # 130 is the standard exit code for SIGINT
    except Exception as e:
        logger.error(f"{Fore.RED}Unhandled error: {e}{Style.RESET_ALL}", exc_info=True)
        sys.exit(1)
