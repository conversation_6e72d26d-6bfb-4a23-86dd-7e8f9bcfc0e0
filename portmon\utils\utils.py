# File: portmon/utils.py
# Description: Enhanced with JSON structured logging, improved target validation, and temp file cleanup.

import logging
import re
import sys
import os
import glob
from logging.handlers import RotatingFileHandler
from pathlib import Path
from typing import Optional, Dict, Any, List, Union, Tuple
from colorama import Fore, Style, init as colorama_init

# For JSON logging
try:
    from pythonjsonlogger import jsonlogger
    JSON_LOGGING_AVAILABLE = True
except ImportError:
    JSON_LOGGING_AVAILABLE = False

colorama_init(autoreset=True)
logger = logging.getLogger(__name__)

LOG_FILENAME = 'port_monitor.log'  # Log file in the current working directory
JSON_LOG_FILENAME = 'port_monitor_json.log'  # JSON formatted log file

# --- Custom Color Formatter ---
class ColorFormatter(logging.Formatter):
    LOG_COLORS = {
        logging.DEBUG: Style.DIM + Fore.CYAN,
        logging.INFO: Fore.BLUE,
        logging.WARNING: Fore.YELLOW,
        logging.ERROR: Fore.RED,
        logging.CRITICAL: Style.BRIGHT + Fore.RED,
    }
    
    def format(self, record):
        log_color = self.LOG_COLORS.get(record.levelno, Style.RESET_ALL)
        level_name = f"{log_color}{record.levelname}{Style.RESET_ALL}"
        # Store original levelname before overwriting
        original_levelname = record.levelname
        record.levelname = level_name
        # Use the original formatter's logic
        formatted_message = super().format(record)
        # Restore original levelname if needed elsewhere (though usually not)
        record.levelname = original_levelname
        return formatted_message


# --- Custom JSON Formatter ---
class CustomJsonFormatter(jsonlogger.JsonFormatter if JSON_LOGGING_AVAILABLE else object):
    """Extended JSON formatter with additional fields."""
    
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)
        log_record['level'] = record.levelname
        log_record['logger'] = record.name
        log_record['timestamp'] = self.formatTime(record, self.datefmt)
        
        # Add thread info for concurrent operations
        log_record['thread_id'] = record.thread
        log_record['thread_name'] = record.threadName
        
        # Add process info
        log_record['process_id'] = record.process
        
        # Add exception info if present
        if record.exc_info:
            log_record['exception'] = self.formatException(record.exc_info)


# --- Logging Setup ---
def setup_logging(log_level=logging.INFO, log_file: str = LOG_FILENAME, enable_json_logging: bool = False, json_log_file: str = JSON_LOG_FILENAME):
    """
    Sets up root logger with console (color) and rotating file handlers, optionally with JSON logging.
    
    Args:
        log_level: Logging level to use
        log_file: Path to the standard log file
        enable_json_logging: Whether to enable JSON structured logging
        json_log_file: Path to the JSON log file (only used if enable_json_logging=True)
    """
    root_logger = logging.getLogger()
    # Clear existing handlers if function is called again (e.g., testing)
    if root_logger.hasHandlers():
        root_logger.handlers.clear()

    root_logger.setLevel(log_level)

    # Console Handler (Colored)
    console_handler = logging.StreamHandler(sys.stdout)  # Use stdout
    console_formatter = ColorFormatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # Standard File Handler (Rotating)
    try:
        # Ensure the log file path is absolute or relative to CWD correctly
        log_file_path = Path(log_file).resolve()
        file_handler = RotatingFileHandler(
            log_file_path,
            maxBytes=5*1024*1024,  # 5 MB
            backupCount=3,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - [%(name)s] - %(message)s',  # Include logger name
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
        log_init_message = f"Logging setup complete. Level: {logging.getLevelName(log_level)}. Log file: {log_file_path}"
        
        # JSON Structured Logging (Optional)
        if enable_json_logging:
            if not JSON_LOGGING_AVAILABLE:
                root_logger.warning("Python JSON logger not available. Install with: pip install python-json-logger")
            else:
                json_log_path = Path(json_log_file).resolve()
                json_handler = RotatingFileHandler(
                    json_log_path,
                    maxBytes=10*1024*1024,  # 10 MB
                    backupCount=5,
                    encoding='utf-8'
                )
                json_formatter = CustomJsonFormatter(
                    '%(timestamp)s %(level)s %(name)s %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S'
                )
                json_handler.setFormatter(json_formatter)
                root_logger.addHandler(json_handler)
                log_init_message += f", JSON log file: {json_log_path}"
        
    except Exception as e:
        # Log error to console if file logging fails
        root_logger.error(f"{Fore.RED}Failed to set up file logging to {log_file}: {e}{Style.RESET_ALL}")
        log_init_message = f"Console logging setup complete. File logging FAILED. Level: {logging.getLevelName(log_level)}."

    # Use the root logger to log the init message so it goes to handlers
    root_logger.info(log_init_message)


# --- Target Validation & Sanitization ---
def sanitize_target_identifier(target: str) -> Optional[str]:
    """
    Sanitize a target identifier to ensure it's safe to use in file paths and shell commands.
    
    This helps prevent path traversal and shell injection issues.
    
    Args:
        target: The target identifier (e.g., 'example.com')
        
    Returns:
        Sanitized target or None if target is invalid
    """
    if not target or not isinstance(target, str):
        logger.error(f"{Fore.RED}[-] Invalid target: {target}{Style.RESET_ALL}")
        return None
        
    # Only allow alphanumeric, hyphens, underscores, and dots for domain names
    pattern = re.compile(r'^[a-zA-Z0-9-.]+$')
    if not pattern.match(target):
        # Replace invalid characters with underscores if possible
        sanitized = re.sub(r'[^a-zA-Z0-9-.]+', '_', target)
        logger.warning(f"{Fore.YELLOW}[!] Target '{target}' contains invalid characters. Sanitized to: '{sanitized}'{Style.RESET_ALL}")
        return sanitized
        
    # Don't allow attempts at directory traversal
    if '..' in target or '/' in target or '\\' in target:
        logger.error(f"{Fore.RED}[-] Target '{target}' contains potential path traversal patterns. Rejected.{Style.RESET_ALL}")
        return None
        
    return target

# --- Path Management ---
def get_target_paths(config: Dict[str, Any], target: str) -> Optional[Dict[str, Path]]:
    """
    Constructs and validates necessary paths for a given target.

    Input subdomains are read from: {recon_base_dir}/{target}/subdomains/all-subdomains.txt
    Output (scans, state) is written relative to the current working directory:
    ./portmon_output/{target}/scans/
    ./portmon_output/{target}/discovered_ports.json

    Args:
        config: The global configuration dictionary.
        target: The target identifier (e.g., 'example.com').

    Returns:
        A dictionary containing Path objects for 'subdomains_file',
        'scan_output_dir', 'state_file', or None if validation fails.
    """
    try:
        # Sanitize target identifier for path safety
        sanitized_target = sanitize_target_identifier(target)
        if not sanitized_target:
            return None
        
        # --- Input Path (based on config) ---
        recon_base_dir = Path(config['recon_base_dir']).resolve() # Resolve to absolute path
        input_target_dir = recon_base_dir / sanitized_target
        subdomains_file = input_target_dir / 'subdomains' / 'all-subdomains.txt'

        # --- Output Paths (relative to current working directory) ---
        # Assumes the script is run from the project root (e.g., /d/bb-automation/portmon)
        output_base_dir = Path.cwd() / 'portmon_output'
        output_target_dir = output_base_dir / sanitized_target
        scan_output_dir = output_target_dir / 'scans'
        state_file = output_target_dir / 'discovered_ports.json'

        # Create necessary output directories automatically
        # state_file's directory (output_target_dir) is created first
        output_target_dir.mkdir(parents=True, exist_ok=True)
        # Then scan_output_dir within it
        scan_output_dir.mkdir(parents=True, exist_ok=True)

        # --- Validate input subdomain file ---
        if not subdomains_file.is_file():
            logger.error(f"{Fore.RED}[-] Required subdomains file not found for target '{sanitized_target}': {subdomains_file}{Style.RESET_ALL}")
            return None
        # Warning for empty file, but allow proceeding (Naabu might handle it)
        if subdomains_file.stat().st_size == 0:
             logger.warning(f"{Fore.YELLOW}[!] Subdomains file exists but is empty for target '{sanitized_target}': {subdomains_file}{Style.RESET_ALL}")

        logger.debug(f"Paths for target '{sanitized_target}': Input='{subdomains_file}', ScanDir='{scan_output_dir}', StateFile='{state_file}'")

        return {
            'subdomains_file': subdomains_file,
            'scan_output_dir': scan_output_dir,
            'state_file': state_file,
        }
    except KeyError as e:
         logger.error(f"{Fore.RED}[-] Missing configuration key needed for path generation: {e}{Style.RESET_ALL}")
         return None
    except Exception as e:
        logger.error(f"{Fore.RED}[-] Error building or validating paths for target '{target}': {e}{Style.RESET_ALL}", exc_info=True)
        return None


# --- Temporary File Cleanup ---
def cleanup_temp_files(scan_output_dir: Path) -> Tuple[int, int]:
    """
    Clean up temporary scan output files after a scan cycle.
    
    Args:
        scan_output_dir: Directory containing scan output files to clean
        
    Returns:
        Tuple of (files_removed, total_bytes_freed)
    """
    if not scan_output_dir or not scan_output_dir.exists():
        logger.warning(f"Cannot clean non-existent scan output directory: {scan_output_dir}")
        return 0, 0
    
    try:
        # Get all files in the scan output directory
        files_to_clean = list(scan_output_dir.glob("*.txt"))
        
        # Count space before deletion
        total_bytes = sum(f.stat().st_size for f in files_to_clean)
        files_count = len(files_to_clean)
        
        # Delete each file
        for file_path in files_to_clean:
            try:
                file_path.unlink()
                logger.debug(f"Removed temporary file: {file_path.name}")
            except Exception as e:
                logger.warning(f"Failed to remove temporary file {file_path.name}: {e}")
        
        # Log results
        logger.info(f"{Fore.GREEN}[+] Cleaned up {files_count} temporary files, freed {total_bytes/1024:.1f}KB{Style.RESET_ALL}")
        return files_count, total_bytes
        
    except Exception as e:
        logger.error(f"{Fore.RED}[-] Error cleaning temporary files: {e}{Style.RESET_ALL}")
        return 0, 0