# File: portmon/utils/file_manager.py
# Description: Advanced file management and operations utilities

import os
import shutil
import logging
import hashlib
import gzip
import json
from typing import Dict, List, Optional, Union, Iterator, Tuple
from pathlib import Path
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class FileInfo:
    """Information about a file."""
    path: Path
    size_bytes: int
    modified_time: datetime
    created_time: datetime
    is_directory: bool
    permissions: str
    checksum: Optional[str] = None

@dataclass
class CleanupResult:
    """Result of file cleanup operation."""
    files_removed: int
    directories_removed: int
    bytes_freed: int
    errors: List[str]

class FileManager:
    """
    Advanced file management utilities for portmon operations.
    """
    
    def __init__(self, base_dir: Optional[Path] = None):
        """
        Initialize file manager.
        
        Args:
            base_dir: Base directory for operations (defaults to current working directory)
        """
        self.base_dir = base_dir or Path.cwd()
        self._ensure_base_dir()
    
    def _ensure_base_dir(self) -> None:
        """Ensure base directory exists."""
        try:
            self.base_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.error(f"Failed to create base directory {self.base_dir}: {e}")
            raise
    
    def get_file_info(self, file_path: Path, calculate_checksum: bool = False) -> Optional[FileInfo]:
        """
        Get detailed information about a file.
        
        Args:
            file_path: Path to the file
            calculate_checksum: Whether to calculate file checksum
            
        Returns:
            File information or None if file doesn't exist
        """
        try:
            if not file_path.exists():
                return None
            
            stat_info = file_path.stat()
            
            # Get timestamps
            modified_time = datetime.fromtimestamp(stat_info.st_mtime, tz=timezone.utc)
            created_time = datetime.fromtimestamp(stat_info.st_ctime, tz=timezone.utc)
            
            # Get permissions
            permissions = oct(stat_info.st_mode)[-3:]
            
            # Calculate checksum if requested
            checksum = None
            if calculate_checksum and file_path.is_file():
                checksum = self.calculate_file_checksum(file_path)
            
            return FileInfo(
                path=file_path,
                size_bytes=stat_info.st_size,
                modified_time=modified_time,
                created_time=created_time,
                is_directory=file_path.is_dir(),
                permissions=permissions,
                checksum=checksum
            )
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return None
    
    def calculate_file_checksum(self, file_path: Path, algorithm: str = 'sha256') -> Optional[str]:
        """
        Calculate checksum for a file.
        
        Args:
            file_path: Path to the file
            algorithm: Hash algorithm to use
            
        Returns:
            Hexadecimal checksum or None on error
        """
        try:
            hash_obj = hashlib.new(algorithm)
            
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_obj.update(chunk)
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate checksum for {file_path}: {e}")
            return None
    
    def find_files(self, pattern: str = "*", max_age_days: Optional[int] = None, 
                   min_size_mb: Optional[float] = None, max_size_mb: Optional[float] = None) -> List[FileInfo]:
        """
        Find files matching criteria.
        
        Args:
            pattern: File pattern to match
            max_age_days: Maximum age in days
            min_size_mb: Minimum size in MB
            max_size_mb: Maximum size in MB
            
        Returns:
            List of matching files
        """
        matching_files = []
        
        try:
            # Find files matching pattern
            for file_path in self.base_dir.rglob(pattern):
                if not file_path.is_file():
                    continue
                
                file_info = self.get_file_info(file_path)
                if not file_info:
                    continue
                
                # Check age filter
                if max_age_days is not None:
                    age = datetime.now(timezone.utc) - file_info.modified_time
                    if age.days > max_age_days:
                        continue
                
                # Check size filters
                size_mb = file_info.size_bytes / (1024 * 1024)
                if min_size_mb is not None and size_mb < min_size_mb:
                    continue
                if max_size_mb is not None and size_mb > max_size_mb:
                    continue
                
                matching_files.append(file_info)
            
            logger.debug(f"Found {len(matching_files)} files matching criteria")
            return matching_files
            
        except Exception as e:
            logger.error(f"Error finding files: {e}")
            return []
    
    def cleanup_old_files(self, max_age_days: int = 7, patterns: List[str] = None, 
                         dry_run: bool = False) -> CleanupResult:
        """
        Clean up old files based on age and patterns.
        
        Args:
            max_age_days: Maximum age in days
            patterns: File patterns to clean (defaults to common temp patterns)
            dry_run: If True, only report what would be deleted
            
        Returns:
            Cleanup result
        """
        if patterns is None:
            patterns = ["*.tmp", "*.temp", "*_temp_*", "naabu_*.txt", "httpx_*.txt"]
        
        result = CleanupResult(
            files_removed=0,
            directories_removed=0,
            bytes_freed=0,
            errors=[]
        )
        
        cutoff_time = datetime.now(timezone.utc) - timedelta(days=max_age_days)
        
        try:
            for pattern in patterns:
                for file_path in self.base_dir.rglob(pattern):
                    try:
                        if not file_path.exists():
                            continue
                        
                        # Check age
                        stat_info = file_path.stat()
                        modified_time = datetime.fromtimestamp(stat_info.st_mtime, tz=timezone.utc)
                        
                        if modified_time > cutoff_time:
                            continue
                        
                        # Count size before deletion
                        if file_path.is_file():
                            result.bytes_freed += stat_info.st_size
                        
                        if dry_run:
                            logger.info(f"Would delete: {file_path}")
                        else:
                            if file_path.is_file():
                                file_path.unlink()
                                result.files_removed += 1
                                logger.debug(f"Deleted file: {file_path}")
                            elif file_path.is_dir():
                                shutil.rmtree(file_path)
                                result.directories_removed += 1
                                logger.debug(f"Deleted directory: {file_path}")
                        
                    except Exception as e:
                        error_msg = f"Failed to delete {file_path}: {e}"
                        result.errors.append(error_msg)
                        logger.warning(error_msg)
            
            if not dry_run:
                logger.info(f"Cleanup complete: {result.files_removed} files, "
                           f"{result.directories_removed} directories, "
                           f"{result.bytes_freed / (1024*1024):.1f}MB freed")
            
            return result
            
        except Exception as e:
            result.errors.append(f"Cleanup operation failed: {e}")
            logger.error(f"Cleanup operation failed: {e}")
            return result
    
    def compress_file(self, file_path: Path, remove_original: bool = False) -> Optional[Path]:
        """
        Compress a file using gzip.
        
        Args:
            file_path: Path to file to compress
            remove_original: Whether to remove original file
            
        Returns:
            Path to compressed file or None on error
        """
        try:
            compressed_path = file_path.with_suffix(file_path.suffix + '.gz')
            
            with open(file_path, 'rb') as f_in:
                with gzip.open(compressed_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            if remove_original:
                file_path.unlink()
                logger.debug(f"Compressed and removed original: {file_path}")
            else:
                logger.debug(f"Compressed file: {file_path} -> {compressed_path}")
            
            return compressed_path
            
        except Exception as e:
            logger.error(f"Failed to compress {file_path}: {e}")
            return None
    
    def decompress_file(self, compressed_path: Path, remove_compressed: bool = False) -> Optional[Path]:
        """
        Decompress a gzip file.
        
        Args:
            compressed_path: Path to compressed file
            remove_compressed: Whether to remove compressed file
            
        Returns:
            Path to decompressed file or None on error
        """
        try:
            if not compressed_path.name.endswith('.gz'):
                logger.error(f"File is not gzip compressed: {compressed_path}")
                return None
            
            output_path = compressed_path.with_suffix('')
            
            with gzip.open(compressed_path, 'rb') as f_in:
                with open(output_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            if remove_compressed:
                compressed_path.unlink()
                logger.debug(f"Decompressed and removed compressed: {compressed_path}")
            else:
                logger.debug(f"Decompressed file: {compressed_path} -> {output_path}")
            
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to decompress {compressed_path}: {e}")
            return None
    
    def backup_file(self, file_path: Path, backup_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Create a backup of a file.
        
        Args:
            file_path: Path to file to backup
            backup_dir: Directory for backups (defaults to base_dir/backups)
            
        Returns:
            Path to backup file or None on error
        """
        try:
            if backup_dir is None:
                backup_dir = self.base_dir / 'backups'
            
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Create backup filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{file_path.stem}_{timestamp}{file_path.suffix}"
            backup_path = backup_dir / backup_name
            
            shutil.copy2(file_path, backup_path)
            logger.debug(f"Created backup: {file_path} -> {backup_path}")
            
            return backup_path
            
        except Exception as e:
            logger.error(f"Failed to backup {file_path}: {e}")
            return None
    
    def get_directory_size(self, directory: Path) -> int:
        """
        Calculate total size of a directory.
        
        Args:
            directory: Directory to analyze
            
        Returns:
            Total size in bytes
        """
        total_size = 0
        
        try:
            for file_path in directory.rglob('*'):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
            
            return total_size
            
        except Exception as e:
            logger.error(f"Failed to calculate directory size for {directory}: {e}")
            return 0
    
    def organize_files_by_date(self, source_dir: Path, target_dir: Path, 
                              date_format: str = "%Y/%m") -> Dict[str, int]:
        """
        Organize files into date-based directory structure.
        
        Args:
            source_dir: Source directory containing files
            target_dir: Target directory for organized files
            date_format: Date format for directory structure
            
        Returns:
            Dictionary with organization statistics
        """
        stats = {"moved": 0, "errors": 0, "directories_created": 0}
        
        try:
            target_dir.mkdir(parents=True, exist_ok=True)
            
            for file_path in source_dir.rglob('*'):
                if not file_path.is_file():
                    continue
                
                try:
                    # Get file modification date
                    mod_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    date_subdir = mod_time.strftime(date_format)
                    
                    # Create target directory
                    target_subdir = target_dir / date_subdir
                    if not target_subdir.exists():
                        target_subdir.mkdir(parents=True, exist_ok=True)
                        stats["directories_created"] += 1
                    
                    # Move file
                    target_file = target_subdir / file_path.name
                    shutil.move(str(file_path), str(target_file))
                    stats["moved"] += 1
                    
                except Exception as e:
                    logger.warning(f"Failed to move {file_path}: {e}")
                    stats["errors"] += 1
            
            logger.info(f"File organization complete: {stats}")
            return stats
            
        except Exception as e:
            logger.error(f"File organization failed: {e}")
            stats["errors"] += 1
            return stats
    
    def save_json_data(self, data: Dict, file_path: Path, backup: bool = True, 
                      compress: bool = False) -> bool:
        """
        Save data to JSON file with optional backup and compression.
        
        Args:
            data: Data to save
            file_path: Target file path
            backup: Whether to backup existing file
            compress: Whether to compress the file
            
        Returns:
            True if successful
        """
        try:
            # Backup existing file if requested
            if backup and file_path.exists():
                self.backup_file(file_path)
            
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Save JSON data
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
            
            # Compress if requested
            if compress:
                compressed_path = self.compress_file(file_path, remove_original=True)
                if compressed_path:
                    logger.debug(f"Saved and compressed JSON data to {compressed_path}")
                else:
                    logger.warning(f"Failed to compress {file_path}")
            else:
                logger.debug(f"Saved JSON data to {file_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save JSON data to {file_path}: {e}")
            return False

# Global file manager instance
_file_manager = FileManager()

def get_file_manager() -> FileManager:
    """Get the global file manager instance."""
    return _file_manager
