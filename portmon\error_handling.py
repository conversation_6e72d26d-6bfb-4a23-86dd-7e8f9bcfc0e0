# File: portmon/error_handling.py
# Description: Enhanced error handling and recovery utilities for portmon

import logging
import time
import traceback
from typing import Optional, Callable, Any, Dict, List
from functools import wraps
from enum import Enum
import threading

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels for categorization."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RetryStrategy(Enum):
    """Different retry strategies."""
    EXPONENTIAL_BACKOFF = "exponential"
    LINEAR_BACKOFF = "linear"
    FIXED_DELAY = "fixed"

class PortmonError(Exception):
    """Base exception class for portmon-specific errors."""
    
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                 recoverable: bool = True, context: Optional[Dict] = None):
        super().__init__(message)
        self.severity = severity
        self.recoverable = recoverable
        self.context = context or {}
        self.timestamp = time.time()

class ScanError(PortmonError):
    """Error during scanning operations."""
    pass

class ConfigurationError(PortmonError):
    """Error in configuration."""
    
    def __init__(self, message: str, context: Optional[Dict] = None):
        super().__init__(message, ErrorSeverity.HIGH, False, context)

class NetworkError(PortmonError):
    """Network-related errors."""
    pass

class ToolError(PortmonError):
    """Error with external tools (naabu, httpx, nmap)."""
    pass

class ErrorTracker:
    """
    Track and analyze errors to identify patterns and suggest recovery actions.
    """
    
    def __init__(self, max_errors: int = 1000):
        self.max_errors = max_errors
        self._errors: List[Dict] = []
        self._error_counts: Dict[str, int] = {}
        self._lock = threading.Lock()
    
    def record_error(self, error: Exception, context: Optional[Dict] = None) -> None:
        """Record an error for analysis."""
        error_info = {
            'timestamp': time.time(),
            'type': type(error).__name__,
            'message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {},
            'severity': getattr(error, 'severity', ErrorSeverity.MEDIUM).value,
            'recoverable': getattr(error, 'recoverable', True)
        }
        
        with self._lock:
            self._errors.append(error_info)
            
            # Keep only recent errors
            if len(self._errors) > self.max_errors:
                self._errors = self._errors[-self.max_errors:]
            
            # Update error counts
            error_type = error_info['type']
            self._error_counts[error_type] = self._error_counts.get(error_type, 0) + 1
        
        # Log based on severity
        if hasattr(error, 'severity'):
            if error.severity == ErrorSeverity.CRITICAL:
                logger.critical(f"Critical error: {error}")
            elif error.severity == ErrorSeverity.HIGH:
                logger.error(f"High severity error: {error}")
            elif error.severity == ErrorSeverity.MEDIUM:
                logger.warning(f"Medium severity error: {error}")
            else:
                logger.info(f"Low severity error: {error}")
        else:
            logger.error(f"Unhandled error: {error}")
    
    def get_error_summary(self, hours: int = 24) -> Dict:
        """Get a summary of errors in the last N hours."""
        cutoff_time = time.time() - (hours * 3600)
        
        with self._lock:
            recent_errors = [e for e in self._errors if e['timestamp'] > cutoff_time]
            
            summary = {
                'total_errors': len(recent_errors),
                'error_types': {},
                'severity_breakdown': {},
                'most_common_errors': []
            }
            
            for error in recent_errors:
                error_type = error['type']
                severity = error['severity']
                
                summary['error_types'][error_type] = summary['error_types'].get(error_type, 0) + 1
                summary['severity_breakdown'][severity] = summary['severity_breakdown'].get(severity, 0) + 1
            
            # Sort by frequency
            summary['most_common_errors'] = sorted(
                summary['error_types'].items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
        
        return summary
    
    def should_circuit_break(self, error_type: str, threshold: int = 10, window_minutes: int = 30) -> bool:
        """Determine if we should circuit break based on error frequency."""
        cutoff_time = time.time() - (window_minutes * 60)
        
        with self._lock:
            recent_errors = [
                e for e in self._errors 
                if e['timestamp'] > cutoff_time and e['type'] == error_type
            ]
        
        return len(recent_errors) >= threshold

def retry_with_backoff(
    max_attempts: int = 3,
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator for retrying functions with configurable backoff strategies.
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        # Last attempt failed
                        logger.error(f"Function {func.__name__} failed after {max_attempts} attempts: {e}")
                        raise
                    
                    # Calculate delay based on strategy
                    if strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
                        delay = min(base_delay * (2 ** attempt), max_delay)
                    elif strategy == RetryStrategy.LINEAR_BACKOFF:
                        delay = min(base_delay * (attempt + 1), max_delay)
                    else:  # FIXED_DELAY
                        delay = base_delay
                    
                    logger.warning(f"Function {func.__name__} failed (attempt {attempt + 1}/{max_attempts}): {e}. Retrying in {delay:.1f}s")
                    time.sleep(delay)
            
            # This should never be reached, but just in case
            raise last_exception
        
        return wrapper
    return decorator

def safe_execute(func: Callable, *args, default_return=None, log_errors: bool = True, **kwargs) -> Any:
    """
    Safely execute a function and return a default value on error.
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_errors:
            logger.error(f"Safe execution of {func.__name__} failed: {e}")
        return default_return

class CircuitBreaker:
    """
    Circuit breaker pattern implementation for failing operations.
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self._lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function through circuit breaker."""
        with self._lock:
            if self.state == "OPEN":
                if time.time() - self.last_failure_time > self.recovery_timeout:
                    self.state = "HALF_OPEN"
                    logger.info("Circuit breaker transitioning to HALF_OPEN")
                else:
                    raise PortmonError("Circuit breaker is OPEN", ErrorSeverity.HIGH)
        
        try:
            result = func(*args, **kwargs)
            
            with self._lock:
                if self.state == "HALF_OPEN":
                    self.state = "CLOSED"
                    self.failure_count = 0
                    logger.info("Circuit breaker reset to CLOSED")
            
            return result
            
        except Exception as e:
            with self._lock:
                self.failure_count += 1
                self.last_failure_time = time.time()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = "OPEN"
                    logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
            
            raise

# Global error tracker instance
_error_tracker = ErrorTracker()

def get_error_tracker() -> ErrorTracker:
    """Get the global error tracker instance."""
    return _error_tracker

def handle_error(error: Exception, context: Optional[Dict] = None) -> None:
    """Global error handler that records and processes errors."""
    _error_tracker.record_error(error, context)
    
    # Additional error handling logic can be added here
    # e.g., sending alerts, triggering recovery actions, etc.
