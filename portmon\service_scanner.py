# File: portmon/service_scanner.py
# Description: Module for running Nmap scans on specific ports with centralized execution.

import logging
import xml.etree.ElementTree as ET
from typing import Optional, List, Dict

from .command_runner import run_command
from .notifier import NmapResult, NmapScriptOutput

logger = logging.getLogger(__name__)

def parse_nmap_xml(xml_output: str) -> Optional[NmapResult]:
    """
    Parses Nmap XML output for a single host/port scan.

    Args:
        xml_output: String containing the Nmap XML output.

    Returns:
        An NmapResult object with extracted service info, or None if parsing fails
        or no relevant info is found.
    """
    try:
        root = ET.fromstring(xml_output)
        host_element = root.find('host')
        if host_element is None:
            logger.debug("No <host> element found in Nmap XML.")
            return None

        port_element = host_element.find('.//port')
        if port_element is None:
            logger.debug("No <port> element found in Nmap XML.")
            return None

        state_element = port_element.find('state')
        if state_element is None or state_element.get('state') != 'open':
            logger.debug(f"Port state is not 'open' in Nmap XML: {state_element.get('state') if state_element is not None else 'N/A'}")
            return None # Only care about open ports

        service_name = 'N/A'
        product = None
        version = None
        service_element = port_element.find('service')
        if service_element is not None:
            service_name = service_element.get('name', 'N/A')
            product = service_element.get('product')  # Might be None
            version = service_element.get('version')  # Might be None

        # Extract script outputs (e.g., banner)
        banner = None
        for script_elem in port_element.findall('script'):
            script_id = script_elem.get('id')
            script_output = script_elem.get('output')
            if script_id and script_output:
                # Simple banner grabbing, can be expanded
                if script_id == 'banner' or 'banner' in script_id:
                    # Limit banner length
                    banner_text = script_output.strip()
                    max_banner = 150
                    if len(banner_text) > max_banner:
                        banner_text = banner_text[:max_banner] + "..."
                    banner = banner_text

                # Additional script outputs could be added here if needed

        script_outputs = NmapScriptOutput(banner=banner) if banner else None

        if service_name == 'N/A' and not script_outputs:  # If service element was missing and no scripts ran/matched
            logger.debug("Nmap XML parsed, but no <service> or relevant <script> data found for open port.")
            return None

        # Return a strongly-typed NmapResult instead of a dictionary
        return NmapResult(
            service_name=service_name,
            product=product,
            version=version,
            script_outputs=script_outputs
        )

    except ET.ParseError as e:
        logger.error(f"Failed to parse Nmap XML: {e}")
        logger.debug(f"Problematic Nmap XML:\n{xml_output[:1000]}...") # Log beginning of XML
        return None
    except Exception as e:
        logger.error(f"Unexpected error parsing Nmap XML: {e}", exc_info=True)
        return None


def run_nmap_batch_scan(config: dict, host: str, ports: List[str]) -> Dict[str, NmapResult]:
    """
    Run one Nmap scan against a host for all ports in a single invocation.
    
    Args:
        config: The global configuration dictionary
        host: Target hostname/IP
        ports: List of port numbers to scan
        
    Returns:
        Dictionary mapping port numbers to NmapResult objects
    """
    if not config.get('nmap_scan_enabled', False):
        logger.debug(f"Nmap batch scan skipped for {host} as nmap_scan_enabled is false.")
        return {}
        
    if not ports:
        logger.debug(f"No ports provided for {host}, skipping Nmap batch scan.")
        return {}
    
    # Join ports into comma-separated list
    port_list = ",".join(ports)
    
    # Configuration
    nmap_path = config.get('nmap_path', 'nmap')
    scan_timeout_seconds = config.get('nmap_timeout_seconds', 300)
    host_timeout_nmap = f"{scan_timeout_seconds}s"
    process_timeout = scan_timeout_seconds + 30
    
    # Use configurable flags from config
    nmap_flags = config.get('nmap_flags', [
        '-sV',          # Service/version detection
        '-sC',          # Default scripts
        '--open',       # Only show ports found to be open
        '-T4',          # Aggressive timing template
        '-Pn',          # Skip host discovery
    ])
    
    # Build command
    cmd = [
        nmap_path,
        '-p', port_list,
        '--host-timeout', host_timeout_nmap,
        '-oX', '-',     # XML output to stdout
    ]
    cmd.extend(nmap_flags)
    cmd.append(host)
    
    logger.info(f"Running Nmap batch scan for {host} on {len(ports)} ports: {' '.join(cmd)}")
    
    # Execute the command
    success, stdout, stderr, _ = run_command(
        cmd=cmd,
        timeout=process_timeout,
        description=f"Nmap batch scan for {host}",
        log_level=logging.INFO,
        output_log_level=logging.DEBUG
    )
    
    if not success:
        logger.warning(f"Nmap batch scan for {host} may have encountered errors.")
        if stderr:
            logger.warning(f"Nmap stderr: {stderr[:200]}{'...' if len(stderr) > 200 else ''}")
            
    if not stdout:
        logger.warning(f"Nmap batch scan produced no output for {host}")
        return {}
    
    # Parse the XML results
    results = {}
    try:
        root = ET.fromstring(stdout)
        
        # Find all port elements and parse them individually
        for port_elem in root.findall(".//port"):
            port_id = port_elem.get('portid')
            
            # Check if the port is open
            state_elem = port_elem.find('state')
            if state_elem is None or state_elem.get('state') != 'open':
                continue
                
            # Extract service info
            service_name = 'N/A'
            product = None
            version = None
            banner = None
            
            # Get service details
            service_elem = port_elem.find('service')
            if service_elem is not None:
                service_name = service_elem.get('name', 'N/A')
                product = service_elem.get('product')
                version = service_elem.get('version')
            
            # Extract any banner info from scripts
            for script_elem in port_elem.findall('script'):
                script_id = script_elem.get('id')
                script_output = script_elem.get('output')
                if script_id and script_output and ('banner' in script_id):
                    banner_text = script_output.strip()
                    max_banner = 150
                    if len(banner_text) > max_banner:
                        banner_text = banner_text[:max_banner] + "..."
                    banner = banner_text
                    break
            
            # Create the result objects
            script_outputs = NmapScriptOutput(banner=banner) if banner else None
            result = NmapResult(
                service_name=service_name,
                product=product,
                version=version,
                script_outputs=script_outputs
            )
            
            # Store in results dictionary
            results[port_id] = result
            
        ports_found = len(results)
        logger.info(f"Nmap batch scan for {host} complete: found service details for {ports_found}/{len(ports)} ports")
        
    except ET.ParseError as e:
        logger.error(f"Failed to parse Nmap XML for {host}: {e}")
        logger.debug(f"Problematic Nmap XML:\n{stdout[:1000]}...")
    except Exception as e:
        logger.error(f"Unexpected error processing Nmap results for {host}: {e}", exc_info=True)
        
    return results


def run_nmap_scan(config: dict, host_port: str) -> Optional[NmapResult]:
    """
    Runs an Nmap scan against a specific host:port for service info using XML output.
    
    NOTE: For better performance with multiple ports, use run_nmap_batch_scan instead.

    Args:
        config: The global configuration dictionary.
        host_port: The "host:port" string.

    Returns:
        NmapResult if service info was extracted, else None.
    """
    if not config.get('nmap_scan_enabled', False):
        logger.debug(f"Nmap scan skipped for {host_port} as nmap_scan_enabled is false.")
        return None

    try:
        host, port = host_port.split(':', 1)
    except ValueError:
        logger.error(f"Invalid host:port format for Nmap scan: '{host_port}'")
        return None

    nmap_path = config.get('nmap_path', 'nmap')
    # Use a relatively quick but informative scan: -sV (version), -sC (default scripts)
    # -T4 for speed, --open to only report open ports, -oX - for XML output to stdout
    # --host-timeout to prevent scans dragging on too long for one host
    scan_timeout_seconds = config.get('nmap_timeout_seconds', 300)
    # Nmap --host-timeout needs a time unit, e.g., '300s' or '5m'
    host_timeout_nmap = f"{scan_timeout_seconds}s"
    # Process timeout slightly longer than Nmap's host timeout
    process_timeout = scan_timeout_seconds + 30

    # Use configurable Nmap flags with sensible defaults
    nmap_flags = config.get('nmap_flags', [
        '-sV',          # Probe open ports to determine service/version info
        '-sC',          # Equivalent to --script=default
        '--open',       # Only show ports found to be open
        '-T4',          # Aggressive timing template
        '-Pn',          # Treat host as online (skip ping discovery)
    ])
    
    # Build the command with config-driven flags
    cmd = [
        nmap_path,
        '-p', port,     # Scan only this specific port
        '--host-timeout', host_timeout_nmap, # Timeout per host
        '-oX', '-',     # Output XML to stdout (always required)
    ]
    
    # Add all configured flags
    cmd.extend(nmap_flags)
    
    # Add target host (always last)
    cmd.append(host)

    logger.info(f"Running Nmap service scan: {' '.join(cmd)}")
    log_prefix = f"Nmap scan for {host}:{port}"
    try:
        # Use the centralized command runner instead of direct subprocess.run
        success, stdout, stderr, _ = run_command(
            cmd=cmd,
            timeout=process_timeout,
            description=f"Nmap scan for {host}:{port}",
            log_level=logging.INFO,
            output_log_level=logging.DEBUG
        )

        if not success:
            # Nmap can return non-zero even if it finds info (e.g., if host discovery was skipped but port unresponsive later)
            # Log as warning, but still try parsing stdout as it might contain partial XML
            logger.warning(f"{log_prefix} may have encountered errors; attempting to parse whatever stdout we got.")
            
            # Surface stderr for debugging when available
            if stderr:
                logger.warning(f"{log_prefix} stderr: {stderr[:200]}{'...' if len(stderr) > 200 else ''}")


        if not stdout:
            logger.warning(f"{log_prefix} produced no standard output.")
            return None

        # Parse the XML output
        nmap_data = parse_nmap_xml(stdout)
        if nmap_data:
            logger.info(f"{log_prefix} successful. Service: {nmap_data.service_name}, Product: {nmap_data.product or 'N/A'}")
            return nmap_data
        else:
            logger.info(f"{log_prefix} completed, but no relevant service info extracted from XML.")
            return None  # Explicitly return None if parsing yielded nothing useful

    except Exception as e:
        logger.error(f"{log_prefix} encountered an unexpected error: {e}", exc_info=True)
        return None