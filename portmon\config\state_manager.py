# File: portmon/state_manager.py
# Description: Improved state management with atomic writes, backup, and service delta tracking

import json
import logging
import os
import shutil
from pathlib import Path
from typing import Dict, Any, Set, Optional, Tuple, List
from datetime import datetime

logger = logging.getLogger(__name__)

# Define state file version for potential future migrations
STATE_FORMAT_VERSION = 1

# Structure of port data with service information
PortData = Dict[str, Any]  # host:port -> {service_info}
StateData = Dict[str, Any]  # Full state with metadata and port data

def normalize_ports(raw) -> Set[str]:
    """
    Normalize ports data to a set of strings, handling both dictionary and set/list inputs.
    
    Args:
        raw: The raw ports data which could be a dict, set, or list
        
    Returns:
        A set of port strings
    """
    return set(raw.keys()) if isinstance(raw, dict) else set(raw)

def load_previous_state(state_file: Path) -> Dict[str, PortData]:
    """
    Loads the previously discovered ports and their service information from a JSON file.
    
    Args:
        state_file: Path to the state file
        
    Returns:
        Dictionary mapping host:port to service information, or empty dict if file doesn't exist
    """
    if state_file.is_file():
        try:
            logger.debug(f"Loading previous state from: {state_file}")
            with state_file.open('r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Handle legacy format (list of strings)
            if isinstance(data, list):
                logger.info(f"Converting legacy state format to new format with service tracking")
                # Convert legacy format (list of strings) to new format
                return {item: {"first_seen": datetime.now().isoformat()} 
                        for item in data if isinstance(item, str) and ':' in item}
            
            # Handle new format (dict with metadata)
            if isinstance(data, dict):
                # Extract just the ports data if this is the new format with metadata
                if "_metadata" in data and "ports" in data:
                    port_data = data["ports"]
                    logger.info(f"Loaded {len(port_data)} ports with service information from state file: {state_file.name}")
                    logger.debug(f"Loaded previous_ports, type={type(port_data)}, count={len(port_data)}")
                    return port_data
                # Otherwise assume it's just a direct port data dict
                else:
                    logger.info(f"Loaded {len(data)} ports from legacy format state file: {state_file.name}")
                    logger.debug(f"Loaded previous_ports, type={type(data)}, count={len(data)}")
                    return data
            
            logger.warning(f"State file {state_file} has invalid format. Starting fresh.")
            logger.debug(f"Returning empty dict due to invalid state file format")
            return {}
            
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from state file {state_file}: {e}. Starting fresh.", exc_info=True)
            # Backup corrupted file
            backup_corrupted_file(state_file)
            return {}
            
        except Exception as e:
            logger.error(f"Failed to load state from {state_file}: {e}. Starting fresh.", exc_info=True)
            return {}
    else:
        logger.info(f"No previous state file found at {state_file}. Starting fresh for this target.")
        return {}

def backup_corrupted_file(file_path: Path) -> None:
    """
    Create a backup of a corrupted file with timestamp.
    
    Args:
        file_path: Path to the file to backup
    """
    if not file_path.exists():
        return
        
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = file_path.with_suffix(f"{file_path.suffix}.corrupted_{timestamp}")
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup of corrupted file: {backup_path}")
    except Exception as e:
        logger.error(f"Failed to backup corrupted file {file_path}: {e}")

def save_current_state(state_file: Path, current_state: Dict[str, Any]) -> bool:
    """
    Save the current state with atomic write operations and backup.
    
    Args:
        state_file: Path to the state file
        current_state: Dict mapping host:port to service information
        
    Returns:
        True if save was successful, False otherwise
    """
    try:
        logger.debug(f"Saving current state to: {state_file}")
        state_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Create full state with metadata
        full_state = {
            "_metadata": {
                "version": STATE_FORMAT_VERSION,
                "updated_at": datetime.now().isoformat(),
            },
            "ports": current_state
        }
        
        # Create backup of current file if it exists
        backup_file = state_file.with_suffix(f"{state_file.suffix}.bak")
        if state_file.exists():
            try:
                shutil.copy2(state_file, backup_file)
                logger.debug(f"Created backup of current state file: {backup_file}")
            except Exception as e:
                logger.warning(f"Failed to create backup of state file: {e}")
        
        # Write to temporary file first
        temp_file = state_file.with_suffix(f"{state_file.suffix}.tmp")
        with temp_file.open('w', encoding='utf-8') as f:
            json.dump(full_state, f, indent=2)
        
        # Atomic rename to final location
        if os.name == 'nt':  # Windows requires removing the destination file first
            if state_file.exists():
                state_file.unlink()
        
        temp_file.rename(state_file)
        logger.info(f"Saved state with {len(current_state)} ports to {state_file.name}")
        return True
        
    except Exception as e:
        logger.error(f"Failed to save state to {state_file}: {e}", exc_info=True)
        return False

def compare_service_info(previous: Dict[str, Any], current: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Compare previous and current service information to detect significant changes.
    
    Args:
        previous: Previous service information
        current: Current service information
        
    Returns:
        Tuple of (changed_flag, list_of_changed_fields)
    """
    if not previous or not current:
        return True, ["first_detection"]
    
    # Fields to consider significant for change detection
    significant_fields = [
        "title", "status_code", "tech", "server", "content_type",  # Httpx fields
        "product", "version", "service_name", "os"  # Nmap fields
    ]
    
    changes = []
    for field in significant_fields:
        prev_value = previous.get(field)
        curr_value = current.get(field)
        
        # Only compare if both have the field
        if prev_value is not None and curr_value is not None:
            if prev_value != curr_value:
                changes.append(field)
    
    return len(changes) > 0, changes