# File: portmon/prober.py
# Description: Refactored to use centralized command_runner with improved error handling and type safety.

import json
import logging
from json import JSONDecodeError
from typing import Optional, Dict, Any, Set, List

from .command_runner import run_command
from .notifier import HttpProbeResult

logger = logging.getLogger(__name__)

def _parse_first_json_line(output: str) -> Optional[Dict[str, Any]]:
    """
    Helper function to extract the first valid JSON object from a multi-line string.
    
    Args:
        output: String output that may contain JSON objects
        
    Returns:
        The first valid JSON object found, or None if no valid JSON is present
    """
    if not output or not output.strip():
        return None
        
    for line in output.splitlines():
        if not line.strip():
            continue
            
        try:
            return json.loads(line)
        except JSONDecodeError:
            continue
            
    return None


def run_httpx_probe(config: Dict[str, Any], host_port: str) -> Optional[HttpProbeResult]:
    """
    Runs httpx on a specific host:port to gather web information.
    Uses common_https_ports from config to determine initial protocol.

    Args:
        config: The global configuration dictionary.
        host_port: The host:port string (e.g., 'sub.example.com:8080').

    Returns:
        HttpProbeResult if the probe yielded valid JSON, otherwise None.
    """
    # Use integers for port comparisons as configured in config_manager
    common_https_ports: Set[int] = set(config.get('common_https_ports', [443]))
    
    try:
        port_str = host_port.split(':')[-1]
        port_int = int(port_str)
        scheme = 'https://' if port_int in common_https_ports else 'http://'
    except (ValueError, IndexError) as e:
        logger.warning(f"Could not determine port from '{host_port}', defaulting scheme to http://: {e}")
        scheme = 'http://'

    url_to_probe = f"{scheme}{host_port}"
    httpx_path = config.get('httpx_path', 'httpx')
    
    # Honor separate timeouts for the probe tool vs process
    probe_timeout = config.get('probe_timeout', 15)  # For httpx -timeout parameter
    process_timeout = config.get('httpx_process_timeout_seconds', probe_timeout + 10)  # For command_runner
    
    threads = str(config.get('httpx_threads', 100))

    cmd = [
        httpx_path,
        '-u', url_to_probe,
        '-silent',
        '-json',
        '-status-code',
        '-title',
        '-tech-detect',
        '-follow-redirects',
        '-timeout', str(probe_timeout),
        '-threads', threads
    ]

    # Use the centralized command_runner instead of direct subprocess.run
    success, stdout, stderr, _ = run_command(
        cmd=cmd,
        timeout=process_timeout,
        description=f"httpx probe for {url_to_probe}",
        log_level=logging.INFO,
        output_log_level=logging.DEBUG
    )

    # Handle command failure case
    if not success:
        logger.warning(f"Httpx probe failed for {url_to_probe}")
        # We'll still try to parse stdout if it exists
        if not stdout:
            return None

    # Parse JSON output using the helper
    probe_data = _parse_first_json_line(stdout) if stdout else None
    
    # If we found valid JSON data
    if probe_data:
        status = probe_data.get('status-code', 'N/A')
        title_str = probe_data.get('title', '')
        tech_list = probe_data.get('tech', [])
        
        # Log concise summary
        log_summary = f"Status {status}"
        if title_str: log_summary += f", Title '{title_str[:30]}...'"
        if tech_list: log_summary += f", Tech Detected"
        logger.info(f"Httpx probe successful for {url_to_probe}: {log_summary}")
        
        # Return HttpProbeResult dataclass instead of raw dictionary
        return HttpProbeResult(
            url=probe_data.get('url'),
            status_code=probe_data.get('status-code'),
            title=probe_data.get('title'),
            tech=probe_data.get('tech', [])
        )
    else:
        logger.info(f"Httpx probe for {url_to_probe} produced no valid JSON data")
        return None