# Portmon - Reorganized Project Structure

## Overview
The portmon tool has been reorganized into a clean, modular structure where each operation has its own dedicated folder. This improves maintainability, readability, and makes it easier to understand the codebase.

## New Directory Structure

```
portmon/
├── __init__.py                    # Main package initialization
├── __main__.py                    # Entry point for python -m portmon
├── main.py                        # Main application logic and orchestration
├── config/                        # Configuration Management
│   ├── __init__.py               # Config package exports
│   ├── config_manager.py         # YAML config loading and validation
│   └── state_manager.py          # Port state persistence and management
├── scanning/                      # Port Scanning Operations
│   ├── __init__.py               # Scanning package exports
│   ├── command_runner.py         # Centralized command execution utility
│   ├── scanner.py                # Port scanning with Naabu
│   ├── prober.py                 # HTTP probing with Httpx
│   └── service_scanner.py        # Service detection with Nmap
├── notifications/                 # Notification System
│   ├── __init__.py               # Notifications package exports
│   ├── notifier.py               # Discord webhook notifications
│   └── http_utils.py             # HTTP utilities and connection pooling
└── utils/                         # Utilities and Support
    ├── __init__.py               # Utils package exports
    ├── utils.py                  # General utilities (logging, paths, cleanup)
    ├── performance.py            # Performance optimizations and monitoring
    └── error_handling.py         # Error handling and recovery mechanisms
```

## Package Responsibilities

### 📁 `config/` - Configuration Management
- **config_manager.py**: Loads and validates YAML configuration using Pydantic schemas
- **state_manager.py**: Manages persistent state for discovered ports across scans

### 📁 `scanning/` - Port Scanning Operations  
- **command_runner.py**: Centralized subprocess execution with error handling and timeouts
- **scanner.py**: Naabu port scanning with optimized scan modes (top ports, custom ports, combined)
- **prober.py**: Httpx HTTP/HTTPS probing for discovered ports
- **service_scanner.py**: Nmap service fingerprinting and version detection

### 📁 `notifications/` - Notification System
- **notifier.py**: Discord webhook notifications with rich embeds and digest mode
- **http_utils.py**: HTTP utilities with connection pooling and rate limiting

### 📁 `utils/` - Utilities and Support
- **utils.py**: General utilities (logging setup, path management, file cleanup)
- **performance.py**: Performance optimizations (caching, rate limiting, memory optimization)
- **error_handling.py**: Comprehensive error handling with retry logic and circuit breakers

## Key Improvements

### 🚀 Performance Optimizations
- **Smart Scanning**: Eliminates duplicate port scans by intelligently combining top ports + custom ports
- **Caching**: Port deduplication to avoid rescanning same port ranges
- **Efficient Service Discovery**: Only scans NEW ports with Nmap instead of all discovered ports
- **Connection Pooling**: Reuses HTTP connections for Discord notifications
- **Memory Optimization**: Better handling of large port sets and target lists

### 🛡️ Enhanced Error Handling
- **Retry Logic**: Exponential backoff for failed operations
- **Circuit Breakers**: Prevents cascading failures
- **Error Tracking**: Comprehensive error analysis and pattern detection
- **Graceful Degradation**: Continues operation even when some components fail

### 🔧 Better Code Organization
- **Separation of Concerns**: Each package has a clear, focused responsibility
- **Clean Imports**: Organized import structure with package-level exports
- **Type Safety**: Fixed type validation issues throughout the codebase
- **Platform Compatibility**: Better Windows/Linux compatibility

### 📊 Monitoring and Observability
- **Performance Monitoring**: Function timing and slow operation detection
- **Enhanced Logging**: Consistent, structured logging across all modules
- **Error Analytics**: Track error patterns and frequencies

## Usage

The reorganized structure maintains the same external API:

```bash
# Run the tool
python -m portmon

# Or directly
python -m portmon.main
```

## Import Examples

```python
# Import from specific packages
from portmon.config import load_config
from portmon.scanning import run_naabu_scan
from portmon.notifications import send_discord_notification
from portmon.utils import setup_logging

# Or import entire packages
from portmon import config, scanning, notifications, utils
```

## Benefits of New Structure

1. **Maintainability**: Related functionality is grouped together
2. **Testability**: Each package can be tested independently  
3. **Scalability**: Easy to add new scanning methods or notification channels
4. **Readability**: Clear separation makes the codebase easier to understand
5. **Modularity**: Packages can be reused or replaced independently

This reorganization transforms portmon from a collection of scripts into a well-structured, professional Python package that's easier to maintain, extend, and debug.
