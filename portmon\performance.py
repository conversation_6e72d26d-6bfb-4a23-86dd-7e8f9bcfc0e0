# File: portmon/performance.py
# Description: Performance optimization utilities for portmon

import time
import logging
import threading
from typing import Dict, Set, Optional, Callable, Any
from collections import defaultdict, deque
from functools import wraps
import hashlib

logger = logging.getLogger(__name__)

class PortDeduplicator:
    """
    Intelligent port deduplication to avoid scanning the same ports multiple times.
    """
    
    def __init__(self, cache_ttl: int = 3600):
        """
        Initialize the port deduplicator.
        
        Args:
            cache_ttl: Time-to-live for cached port results in seconds
        """
        self.cache_ttl = cache_ttl
        self._port_cache: Dict[str, Dict[str, Any]] = {}
        self._lock = threading.Lock()
    
    def get_cache_key(self, target: str, ports: Set[int]) -> str:
        """Generate a cache key for a target and port set."""
        port_hash = hashlib.md5(str(sorted(ports)).encode()).hexdigest()[:8]
        return f"{target}:{port_hash}"
    
    def is_cached(self, target: str, ports: Set[int]) -> bool:
        """Check if a port scan result is cached and still valid."""
        cache_key = self.get_cache_key(target, ports)
        
        with self._lock:
            if cache_key in self._port_cache:
                cached_time = self._port_cache[cache_key].get('timestamp', 0)
                if time.time() - cached_time < self.cache_ttl:
                    return True
                else:
                    # Remove expired cache entry
                    del self._port_cache[cache_key]
        
        return False
    
    def get_cached_result(self, target: str, ports: Set[int]) -> Optional[Set[str]]:
        """Get cached scan result if available and valid."""
        cache_key = self.get_cache_key(target, ports)
        
        with self._lock:
            if cache_key in self._port_cache:
                cached_time = self._port_cache[cache_key].get('timestamp', 0)
                if time.time() - cached_time < self.cache_ttl:
                    logger.debug(f"Using cached scan result for {target} ({len(ports)} ports)")
                    return self._port_cache[cache_key]['result']
        
        return None
    
    def cache_result(self, target: str, ports: Set[int], result: Set[str]) -> None:
        """Cache a scan result."""
        cache_key = self.get_cache_key(target, ports)
        
        with self._lock:
            self._port_cache[cache_key] = {
                'result': result.copy(),
                'timestamp': time.time()
            }
            logger.debug(f"Cached scan result for {target} ({len(ports)} ports -> {len(result)} open)")
    
    def clear_expired(self) -> int:
        """Clear expired cache entries and return count of removed entries."""
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, data in self._port_cache.items():
                if current_time - data.get('timestamp', 0) >= self.cache_ttl:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._port_cache[key]
        
        if expired_keys:
            logger.debug(f"Cleared {len(expired_keys)} expired cache entries")
        
        return len(expired_keys)

class RateLimiter:
    """
    Advanced rate limiter with burst support and adaptive timing.
    """
    
    def __init__(self, max_requests: int, time_window: float, burst_size: Optional[int] = None):
        """
        Initialize the rate limiter.
        
        Args:
            max_requests: Maximum requests allowed in time_window
            time_window: Time window in seconds
            burst_size: Maximum burst size (defaults to max_requests)
        """
        self.max_requests = max_requests
        self.time_window = time_window
        self.burst_size = burst_size or max_requests
        self._requests = deque()
        self._lock = threading.Lock()
    
    def acquire(self, timeout: Optional[float] = None) -> bool:
        """
        Acquire permission to make a request.
        
        Args:
            timeout: Maximum time to wait for permission
            
        Returns:
            True if permission granted, False if timeout exceeded
        """
        start_time = time.time()
        
        while True:
            with self._lock:
                now = time.time()
                
                # Remove old requests outside the time window
                while self._requests and now - self._requests[0] > self.time_window:
                    self._requests.popleft()
                
                # Check if we can make a request
                if len(self._requests) < self.max_requests:
                    self._requests.append(now)
                    return True
            
            # Check timeout
            if timeout is not None and time.time() - start_time > timeout:
                return False
            
            # Calculate wait time
            if self._requests:
                oldest_request = self._requests[0]
                wait_time = self.time_window - (time.time() - oldest_request)
                if wait_time > 0:
                    time.sleep(min(wait_time, 0.1))  # Sleep in small increments
            else:
                time.sleep(0.01)  # Small delay if no requests in queue

def performance_monitor(func: Callable) -> Callable:
    """
    Decorator to monitor function performance and log slow operations.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            # Log slow operations (>30 seconds)
            if duration > 30:
                logger.warning(f"Slow operation: {func.__name__} took {duration:.2f}s")
            elif duration > 10:
                logger.info(f"Operation: {func.__name__} took {duration:.2f}s")
            else:
                logger.debug(f"Operation: {func.__name__} took {duration:.2f}s")
            
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"Failed operation: {func.__name__} failed after {duration:.2f}s: {e}")
            raise
    
    return wrapper

class MemoryOptimizer:
    """
    Memory optimization utilities for large scan operations.
    """
    
    @staticmethod
    def optimize_port_sets(port_sets: Dict[str, Set[str]]) -> Dict[str, Set[str]]:
        """
        Optimize memory usage of port sets by deduplicating common ports.
        """
        # Find common ports across all targets
        all_ports = set()
        for ports in port_sets.values():
            all_ports.update(ports)
        
        # Use frozensets for immutable, hashable port collections
        optimized = {}
        for target, ports in port_sets.items():
            optimized[target] = frozenset(ports)
        
        logger.debug(f"Optimized {len(port_sets)} port sets with {len(all_ports)} unique ports")
        return optimized
    
    @staticmethod
    def chunk_targets(targets: list, chunk_size: int = 10) -> list:
        """
        Split targets into chunks for memory-efficient processing.
        """
        chunks = []
        for i in range(0, len(targets), chunk_size):
            chunks.append(targets[i:i + chunk_size])
        
        logger.debug(f"Split {len(targets)} targets into {len(chunks)} chunks of max size {chunk_size}")
        return chunks

# Global instances for reuse
_port_deduplicator = PortDeduplicator()
_rate_limiter = RateLimiter(max_requests=10, time_window=60.0)

def get_port_deduplicator() -> PortDeduplicator:
    """Get the global port deduplicator instance."""
    return _port_deduplicator

def get_rate_limiter() -> RateLimiter:
    """Get the global rate limiter instance."""
    return _rate_limiter
