# Portmon – Port Monitoring Tool

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A port monitoring tool that scans target domains for newly opened or closed network ports. It leverages [Naabu](https://github.com/projectdiscovery/naabu) for efficient port scanning, [Httpx](https://github.com/projectdiscovery/httpx) for probing web services, and [Nmap](https://nmap.org/) for service fingerprinting, sending notifications via Discord upon detecting changes.

## Features

### 🔧 **Core Functionality**

- **Dynamic Configuration**:
  - Real-time config reload via SIGHUP (POSIX) or file modification detection
  - Schema-based configuration validation with detailed error reporting
  - Environment variable overrides with `PORTMON_*` prefix
  - Flexible port selection: top ports, custom ports, exclusions
- **Port Scanning**:
  - Regular scanning with configurable intervals
  - Daily top 1000 ports scan
  - Input validation and metrics tracking
  - Configurable Naabu scan rate and concurrency
- **Service Detection**:
  - Smart HTTPS probing based on common HTTPS ports list
  - Configurable Httpx threads for web service detection
  - Optional Nmap service fingerprinting with timeout controls
  - Performance metrics and timing analysis

### 🚨 **Security & Monitoring**

- **Critical Port Monitoring**:
  - Define sensitive ports for immediate alerts
  - Separate notification paths for critical vs regular ports
- **Security Validation**:
  - Comprehensive input sanitization and validation
  - Path traversal prevention and command injection protection
  - Domain and IP address validation with security checks
- **System Monitoring**:
  - Real-time CPU, memory, and disk usage monitoring
  - System requirements checking and optimization recommendations
  - Process-level resource tracking

### 📢 **Enhanced Notifications**

- **Multi-Channel Support**:
  - Discord webhooks with rich embeds
  - Email notifications with HTML formatting
  - Extensible provider architecture for Slack, Teams, etc.
- **Notification Management**:
  - Immediate alerts for critical port discoveries
  - Choice of immediate or digest mode for regular ports
  - Delivery metrics and success rate tracking
  - Fallback mechanisms and retry logic

### ⚡ **Performance & Reliability**

- **Performance Controls**:
  - Rate limiting for Naabu scans
  - Concurrent target processing with thread safety
  - Connection pooling and HTTP session management
  - Configurable timeouts for all operations
- **Robust State Management**:
  - JSON-based state tracking with validation
  - Automated file cleanup and compression
  - Error resilient with automatic retries
  - Circuit breaker patterns for fault tolerance
- **Advanced File Management**:
  - Automated cleanup with age and pattern filtering
  - File compression and backup management
  - Directory organization and size analysis

## Prerequisites

- Python 3.7+
- Naabu ([Install Guide](https://github.com/projectdiscovery/naabu#installation))
- Httpx ([Install Guide](https://github.com/projectdiscovery/httpx#installation))
- Nmap ([Install Guide](https://nmap.org/download.html))

## Configuration

Create `config.yaml` in the project root:

```yaml
# --- Core Settings ---
targets:
  - example.com
  - another.org

recon_base_dir: /path/to/recon/data

# --- Scan Settings ---
scan_interval_seconds: 7200 # How often to run regular cycle
max_concurrent_targets: 5 # How many targets to scan in parallel

# --- Tool Performance Settings ---
naabu_path: "naabu" # Path to naabu binary
naabu_rate: 1000 # Packets per second (0 = naabu default)
naabu_concurrency: 100 # Concurrent host scans

httpx_path: "httpx" # Path to httpx binary
httpx_threads: 100 # Concurrent probes
probe_timeout: 15 # Httpx probe timeout

# --- Port Settings ---
top_ports: 1000 # Used if custom_ports is empty

custom_ports: # Specific ports to scan (overrides top_ports)
  - 22
  - 8080
  - 8443

exclude_ports: # Always exclude these ports
  - 80
  - 443

# Ports to try HTTPS first before HTTP
common_https_ports:
  - 443
  - 8443
  - 9443
  - 8001
  - 8081
  - 10000

# Critical ports triggering immediate alerts
critical_ports:
  - 21 # FTP
  - 22 # SSH
  - 23 # Telnet
  - 3389 # RDP
  - 3306 # MySQL

# --- Service Fingerprinting ---
nmap_path: "nmap"
nmap_scan_enabled: true
nmap_timeout_seconds: 300 # Per port scan

# --- Notification Settings ---
discord_webhook_url: "YOUR_WEBHOOK_URL"
notification_mode: "digest" # 'immediate' or 'digest' for non-critical ports
discord_retry_attempts: 3
discord_retry_delay: 5

# Email notifications (optional)
email:
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  from_email: "<EMAIL>"
  to_emails: ["<EMAIL>", "<EMAIL>"]
  username: "<EMAIL>"
  password: "app_password"
  use_tls: true
```

## Directory Structure

```text
/recon_base_dir/
├── example.com/
│   ├── subdomains/
│   │   └── all-subdomains.txt    # Input: One subdomain per line
│   └── portmon_data/             # Created automatically
│       ├── scans/                # Raw Naabu outputs
│       └── discovered_ports.json # State file
└── another.org/
    └── ...
```

## Usage

### Main Application

Run in continuous monitoring mode:

```bash
python -m portmon.main
```

### Management CLI Tools

Portmon includes comprehensive CLI tools for management and testing:

#### System Status and Configuration

```bash
# Check system status and requirements
python -m portmon.utils.utils_cli status

# Validate configuration file
python -m portmon.utils.utils_cli validate-config config.yaml

# Test security validation functions
python -m portmon.utils.utils_cli test-security
```

#### Notification Testing

```bash
# Test Discord webhook
python -m portmon.notifications.test_notifications discord "https://discord.com/api/webhooks/..."

# Test email configuration
python -m portmon.notifications.test_notifications email \
  --smtp-server smtp.gmail.com \
  --smtp-port 587 \
  --from-email <EMAIL> \
  --to-emails <EMAIL>

# Show notification metrics
python -m portmon.notifications.test_notifications metrics
```

#### Scanning Performance

```bash
# Show scanning performance metrics
python -m portmon.scanning.scan_monitor metrics

# Test input validation functions
python -m portmon.scanning.scan_monitor validate
```

#### File Management

```bash
# Clean up old files
python -m portmon.utils.utils_cli cleanup /path/to/data --max-age 7 --dry-run

# Analyze directory structure
python -m portmon.utils.utils_cli analyze /path/to/analyze
```

### Dynamic Configuration Reload

On POSIX systems (Linux/macOS):

1. Find the process ID: `ps aux | grep portmon`
2. Send SIGHUP: `kill -HUP <PID>`

On all systems:

- Modify config.yaml while running
- Changes detected automatically on next cycle

## Logging

- Console output with colors
- Rotating log file: `port_monitor.log`
- Discord notifications for errors

## License

MIT License - See LICENSE file for details
