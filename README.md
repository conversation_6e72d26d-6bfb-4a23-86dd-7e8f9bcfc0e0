# Portmon – Port Monitoring Tool

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A port monitoring tool that scans target domains for newly opened or closed network ports. It leverages [Naabu](https://github.com/projectdiscovery/naabu) for efficient port scanning, [Httpx](https://github.com/projectdiscovery/httpx) for probing web services, and [Nmap](https://nmap.org/) for service fingerprinting, sending notifications via Discord upon detecting changes.

## Features

- **Dynamic Configuration**:
  - Real-time config reload via SIGHUP (POSIX) or file modification detection
  - Highly configurable scan parameters and performance settings
  - Flexible port selection: top ports, custom ports, exclusions
- **Port Scanning**:
  - Regular scanning with configurable intervals
  - Daily top 1000 ports scan
  - Configurable Naabu scan rate and concurrency
- **Service Detection**:
  - Smart HTTPS probing based on common HTTPS ports list
  - Configurable Httpx threads for web service detection
  - Optional Nmap service fingerprinting with timeout controls
- **Critical Port Monitoring**:
  - Define sensitive ports for immediate alerts
  - Separate notification paths for critical vs regular ports
- **Notifications**:
  - Immediate alerts for critical port discoveries
  - Choice of immediate or digest mode for regular ports
  - Detailed Discord notifications with service info
- **Performance Controls**:
  - Rate limiting for Naabu scans
  - Concurrent target processing
  - Configurable timeouts for all operations
- **Robust State Management**:
  - JSON-based state tracking
  - Rotating log files
  - Error resilient with automatic retries

## Prerequisites

- Python 3.7+
- Naabu ([Install Guide](https://github.com/projectdiscovery/naabu#installation))
- Httpx ([Install Guide](https://github.com/projectdiscovery/httpx#installation))
- Nmap ([Install Guide](https://nmap.org/download.html))

## Configuration

Create `config.yaml` in the project root:

```yaml
# --- Core Settings ---
targets:
  - example.com
  - another.org

recon_base_dir: /path/to/recon/data

# --- Scan Settings ---
scan_interval_seconds: 7200 # How often to run regular cycle
max_concurrent_targets: 5 # How many targets to scan in parallel

# --- Tool Performance Settings ---
naabu_path: "naabu" # Path to naabu binary
naabu_rate: 1000 # Packets per second (0 = naabu default)
naabu_concurrency: 100 # Concurrent host scans

httpx_path: "httpx" # Path to httpx binary
httpx_threads: 100 # Concurrent probes
probe_timeout: 15 # Httpx probe timeout

# --- Port Settings ---
top_ports: 1000 # Used if custom_ports is empty

custom_ports: # Specific ports to scan (overrides top_ports)
  - 22
  - 8080
  - 8443

exclude_ports: # Always exclude these ports
  - 80
  - 443

# Ports to try HTTPS first before HTTP
common_https_ports:
  - 443
  - 8443
  - 9443
  - 8001
  - 8081
  - 10000

# Critical ports triggering immediate alerts
critical_ports:
  - 21 # FTP
  - 22 # SSH
  - 23 # Telnet
  - 3389 # RDP
  - 3306 # MySQL

# --- Service Fingerprinting ---
nmap_path: "nmap"
nmap_scan_enabled: true
nmap_timeout_seconds: 300 # Per port scan

# --- Notification Settings ---
discord_webhook_url: "YOUR_WEBHOOK_URL"
notification_mode: "digest" # 'immediate' or 'digest' for non-critical ports
discord_retry_attempts: 3
discord_retry_delay: 5
```

## Directory Structure

```text
/recon_base_dir/
├── example.com/
│   ├── subdomains/
│   │   └── all-subdomains.txt    # Input: One subdomain per line
│   └── portmon_data/             # Created automatically
│       ├── scans/                # Raw Naabu outputs
│       └── discovered_ports.json # State file
└── another.org/
    └── ...
```

## Usage

Run in continuous monitoring mode:

```bash
python -m portmon.main
```

### Dynamic Configuration Reload

On POSIX systems (Linux/macOS):

1. Find the process ID: `ps aux | grep portmon`
2. Send SIGHUP: `kill -HUP <PID>`

On all systems:

- Modify config.yaml while running
- Changes detected automatically on next cycle

## Logging

- Console output with colors
- Rotating log file: `port_monitor.log`
- Discord notifications for errors

## License

MIT License - See LICENSE file for details
