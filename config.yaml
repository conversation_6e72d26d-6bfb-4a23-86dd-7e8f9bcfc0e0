# config.yaml

# --- Core Settings ---
targets:
  - bexio.com
  - kpt.ch
  - szkb.ch
  - valiant.ch
  - thawte.com
  - carvana.com
  - jungfrau.ch
  - baloise.com
  - lienhardt.ch
  - sak.ch
  - kkg.ch
  - adcubum.com
  - ge.ch
  - budgetdirect.com.au
  - pokemon.com
  - services.klaviyo.com
  - staging.sentryx.io
  - volkswagen.de
  - geotrust.com
  - walker-cloud.com
  - junelife.com
  - aboutyou.de

# Where your recon data lives
recon_base_dir: /home/<USER>/my_recon

# --- Scan Settings ---
scan_interval_seconds: 7200 # How often to run regular cycle (seconds)
max_concurrent_targets: 5 # How many targets to scan in parallel

# --- Tool Performance Settings ---
naabu_path: "naabu" # Path to naabu binary
naabu_rate: 5000 # Packets per second (0 = naabu default)
naabu_concurrency: 150 # Concurrent host scans
# Custom naabu flags (excluding -l, -o, -c, -rate which are managed internally)
naabu_flags:
  - "-silent"        # Silent output (no unnecessary info)
  - "-stats"         # Display stats of the running scan
  - "-ping"          # Use ping probe for host discovery
  - "-exclude-cdn"   # Skip full port scans for CDNs (only scan for standard ports)

httpx_path: "httpx" # Path to httpx binary
httpx_threads: 100 # Concurrent probes
probe_timeout: 10 # Httpx probe timeout

# --- Port Settings ---
# For regular scans: top N ports (ignored if custom_ports is non-empty)
top_ports: 1000

# --- Cleanup Settings ---
# Whether to automatically delete temporary files after each scan cycle
cleanup_temp_files: true

# For regular scans: specific ports to scan instead of top_ports
custom_ports:
  - 25    # SMTP
  - 81    # Alternate HTTP
  - 300
  - 389   # LDAP
  - 465   # SMTPS
  - 587   # Email submission
  - 591
  - 593
  - 832
  - 981
  - 990   # FTPS
  - 1010
  - 1099  # RMI Registry
  - 1311
  - 2082  # cPanel
  - 2095  # Webmail
  - 2096  # Webmail/SSL
  - 2181  # ZooKeeper
  - 2375  # Docker
  - 2376  # Docker/SSL
  - 2380  # etcd
  - 2480
  - 3000  # Development servers
  - 3128  # Proxy
  - 3333
  - 4243
  - 4567
  - 4711
  - 4712
  - 4993
  - 5000  # Flask/Docker registry
  - 5104
  - 5108
  - 5280
  - 5281
  - 5601  # Kibana
  - 5672  # RabbitMQ
  - 5800  # VNC web
  - 5984  # CouchDB
  - 6379  # Redis
  - 6443  # Kubernetes API
  - 6543
  - 7000
  - 7001  # WebLogic
  - 7199  # Cassandra
  - 7396
  - 7474  # Neo4j
  - 8000  # Common web
  - 8001  # Common web
  - 8008  # Alternate HTTP
  - 8014
  - 8042
  - 8060
  - 8069  # Odoo
  - 8080  # Alternate HTTP
  - 8081  # Alternate HTTP
  - 8083
  - 8088
  - 8090
  - 8091  # Elasticsearch
  - 8095
  - 8118
  - 8123
  - 8161  # ActiveMQ
  - 8172
  - 8181
  - 8200  # Vault
  - 8222
  - 8243
  - 8280
  - 8281
  - 8333  # Bitcoin
  - 8337
  - 8443  # HTTPS alternate
  - 8500  # Consul
  - 8834  # Nessus
  - 8880
  - 8888  # Common dev
  - 8983  # Solr
  - 9000  # SonarQube
  - 9001  # Supervisor
  - 9043
  - 9060  # WebSphere
  - 9080  # WebSphere HTTP
  - 9090  # Prometheus
  - 9091  # Prometheus push
  - 9092  # Kafka
  - 9200  # Elasticsearch
  - 9443  # WebSphere HTTPS
  - 9502
  - 9800
  - 9981
  - 10000 # Webmin
  - 10250 # Kubernetes
  - 11371 # OpenPGP
  - 12443 # HTTPS alternate
  - 15672 # RabbitMQ management
  - 16080
  - 17778
  - 18091 # Couchbase
  - 18092 # Couchbase
  - 20720
  - 26379 # Redis Sentinel
  - 28017 # MongoDB web
  - 30920
  - 32000
  - 50070 # Hadoop
  - 55440
  - 55672 # RabbitMQ



# Always exclude these ports, even if they show up
exclude_ports:
  - 22
  - 80
  - 443
  - 8080
  - 8443

# Ports to try HTTPS first before HTTP
common_https_ports:
  - 443
  - 8443
  - 9443
  - 8001
  - 8081
  - 10000
  - 10443
  - 12443
  - 4443
  - 7443
  - 8090
  - 8091

# --- Critical Port Settings ---
critical_ports:
  - 21 # FTP
  - 22 # SSH
  - 23 # Telnet
  - 25 # SMTP
  - 110 # POP3
  - 139 # NetBIOS
  - 143 # IMAP
  - 445 # SMB
  - 993 # IMAPS
  - 995 # POP3S
  - 1433 # MSSQL
  - 1521 # Oracle DB
  - 3306 # MySQL/MariaDB
  - 3389 # RDP
  - 5432 # PostgreSQL
  - 5900 # VNC
  - 5901 # VNC
  - 6379 # Redis
  - 27017 # MongoDB
  - 27018 # MongoDB (secondary)
  - 50000 # SAP

# --- Service Fingerprinting ---
nmap_path: "nmap" # Path to nmap binary
nmap_scan_enabled: true
nmap_timeout_seconds: 300 # Per port scan
# Custom nmap flags (excluding -p, --host-timeout, and -oX which are managed internally)
nmap_flags:
  - "-sV"          # Service/version detection
  - "-sC"          # Default scripts
  - "--open"       # Only show ports found to be open
  - "-T4"          # Aggressive timing template
  - "-Pn"          # Skip host discovery
  - "--min-rate 5000"
  - "--max-rtt-timeout 500ms"

# --- Notification Settings ---
discord_webhook_url: "https://discord.com/api/webhooks/1366398340455207064/ChhDigPkR72LKFbtjLBmnYozG5jsI0dvJtj9Ajfj7Lc6wcMU24Dht5tCoIlvbcF409Nd"
notification_mode: "digest" # 'immediate' or 'digest' for non-critical ports
discord_retry_attempts: 3
discord_retry_delay: 5
