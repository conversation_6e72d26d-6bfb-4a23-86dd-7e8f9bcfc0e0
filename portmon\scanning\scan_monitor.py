#!/usr/bin/env python3
# File: portmon/scanning/scan_monitor.py
# Description: Scanning performance monitoring and testing CLI tool

import sys
import argparse
import logging
import time
from typing import Dict, Any, List

from .scanner import (
    get_scan_metrics, 
    validate_target, 
    validate_ports,
    run_naabu_scan
)
from .prober import (
    get_probe_metrics,
    validate_host_port,
    run_httpx_probe
)
from .service_scanner import (
    get_service_scan_metrics,
    validate_ports_for_nmap
)

def setup_logging(verbose: bool = False) -> None:
    """Setup logging for the monitoring tool."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s'
    )

def show_scan_metrics() -> None:
    """Display comprehensive scanning metrics."""
    print("📊 Scanning Performance Metrics")
    print("=" * 50)
    
    # Port scanning metrics
    scan_metrics = get_scan_metrics()
    print(f"\n🔍 Port Scanning (Naabu):")
    print(f"  • Total scans: {scan_metrics.total_scans}")
    print(f"  • Successful scans: {scan_metrics.successful_scans}")
    print(f"  • Failed scans: {scan_metrics.failed_scans}")
    print(f"  • Success rate: {scan_metrics.get_success_rate():.1f}%")
    print(f"  • Total ports found: {scan_metrics.total_ports_found}")
    print(f"  • Average scan time: {scan_metrics.get_average_scan_time():.2f}s")
    print(f"  • Average ports per scan: {scan_metrics.get_average_ports_per_scan():.1f}")
    
    if scan_metrics.last_scan_time:
        print(f"  • Last scan: {scan_metrics.last_scan_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # HTTP probing metrics
    probe_metrics = get_probe_metrics()
    print(f"\n🌐 HTTP Probing (Httpx):")
    print(f"  • Total probes: {probe_metrics.total_probes}")
    print(f"  • Successful probes: {probe_metrics.successful_probes}")
    print(f"  • Failed probes: {probe_metrics.failed_probes}")
    print(f"  • Success rate: {probe_metrics.get_success_rate():.1f}%")
    print(f"  • Average probe time: {probe_metrics.get_average_probe_time():.2f}s")
    
    if probe_metrics.last_probe_time:
        print(f"  • Last probe: {probe_metrics.last_probe_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # Service scanning metrics
    service_metrics = get_service_scan_metrics()
    print(f"\n🔧 Service Scanning (Nmap):")
    print(f"  • Total scans: {service_metrics.total_scans}")
    print(f"  • Successful scans: {service_metrics.successful_scans}")
    print(f"  • Failed scans: {service_metrics.failed_scans}")
    print(f"  • Success rate: {service_metrics.get_success_rate():.1f}%")
    print(f"  • Total services found: {service_metrics.total_services_found}")
    print(f"  • Average scan time: {service_metrics.get_average_scan_time():.2f}s")
    print(f"  • Average services per scan: {service_metrics.get_average_services_per_scan():.1f}")
    
    if service_metrics.last_scan_time:
        print(f"  • Last scan: {service_metrics.last_scan_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

def validate_inputs(args) -> Dict[str, bool]:
    """Validate various input formats."""
    print("🔍 Input Validation Tests")
    print("=" * 30)
    
    results = {}
    
    # Test target validation
    test_targets = [
        "example.com",
        "***********", 
        "sub.example.com",
        "invalid..domain",
        "999.999.999.999",
        "",
        "a" * 300
    ]
    
    print("\n🎯 Target Validation:")
    for target in test_targets:
        is_valid = validate_target(target)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        display_target = target[:30] + "..." if len(target) > 30 else target
        print(f"  • '{display_target}': {status}")
        results[f"target_{target[:10]}"] = is_valid
    
    # Test port validation
    test_ports = [
        [80, 443, 8080],
        [1, 65535],
        [0, 80],  # Invalid: port 0
        [80, 65536],  # Invalid: port > 65535
        [],  # Invalid: empty
        ["80", "443"]  # Invalid: strings
    ]
    
    print("\n🔌 Port Validation:")
    for i, ports in enumerate(test_ports):
        is_valid = validate_ports(ports)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  • {ports}: {status}")
        results[f"ports_{i}"] = is_valid
    
    # Test host:port validation
    test_host_ports = [
        "example.com:80",
        "***********:443",
        "sub.example.com:8080",
        "example.com:0",  # Invalid: port 0
        "example.com:65536",  # Invalid: port > 65535
        "example.com",  # Invalid: no port
        ":80",  # Invalid: no host
        ""  # Invalid: empty
    ]
    
    print("\n🌐 Host:Port Validation:")
    for host_port in test_host_ports:
        is_valid = validate_host_port(host_port)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  • '{host_port}': {status}")
        results[f"hostport_{host_port.replace(':', '_')}"] = is_valid
    
    # Test Nmap port validation
    test_nmap_ports = [
        ["80", "443", "8080"],
        ["1", "65535"],
        ["0", "80"],  # Invalid: port 0
        ["80", "65536"],  # Invalid: port > 65535
        [],  # Invalid: empty
        [80, 443]  # Invalid: integers instead of strings
    ]
    
    print("\n🔧 Nmap Port Validation:")
    for i, ports in enumerate(test_nmap_ports):
        is_valid = validate_ports_for_nmap(ports)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  • {ports}: {status}")
        results[f"nmap_ports_{i}"] = is_valid
    
    return results

def benchmark_scanning(target: str, config: Dict[str, Any]) -> None:
    """Run a benchmark scan to test performance."""
    print(f"🏃‍♂️ Running benchmark scan on {target}")
    print("=" * 40)
    
    # This would require actual scanning setup
    print("⚠️  Benchmark scanning requires full portmon configuration")
    print("   Use the main portmon application for actual scanning tests")

def main():
    """Main entry point for the scanning monitor tool."""
    parser = argparse.ArgumentParser(
        description="Portmon scanning performance monitoring and testing tool"
    )
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Metrics command
    subparsers.add_parser('metrics', help='Show scanning performance metrics')
    
    # Validation command
    subparsers.add_parser('validate', help='Test input validation functions')
    
    # Benchmark command
    benchmark_parser = subparsers.add_parser('benchmark', help='Run scanning benchmark')
    benchmark_parser.add_argument('target', help='Target to benchmark')
    benchmark_parser.add_argument('--config', help='Configuration file path')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    setup_logging(args.verbose)
    
    try:
        if args.command == 'metrics':
            show_scan_metrics()
            return 0
            
        elif args.command == 'validate':
            results = validate_inputs(args)
            total_tests = len(results)
            passed_tests = sum(1 for result in results.values() if result)
            failed_tests = total_tests - passed_tests
            
            print(f"\n📈 Validation Summary:")
            print(f"  • Total tests: {total_tests}")
            print(f"  • Passed: {passed_tests}")
            print(f"  • Failed: {failed_tests}")
            
            return 0 if failed_tests == 0 else 1
            
        elif args.command == 'benchmark':
            config = {}
            if args.config:
                import yaml
                with open(args.config, 'r') as f:
                    config = yaml.safe_load(f)
            
            benchmark_scanning(args.target, config)
            return 0
            
        else:
            print(f"Unknown command: {args.command}")
            return 1
            
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
