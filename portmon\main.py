# File: portmon/main.py
# Description: Port monitoring tool with critical port handling, Nmap scan integration, and dynamic config reload.

import sys
import os
import time
import logging
import argparse
import signal  # For signal handling
import datetime
from datetime import date
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from collections import defaultdict
from typing import List, Dict, Any, Set, Tuple
from colorama import Fore, Style, init as colorama_init
import copy

from .config import load_config, get_last_daily_scan_date, update_last_daily_scan_date, CONFIG_FILE
from .config import load_previous_state, save_current_state, normalize_ports
from .utils import setup_logging, get_target_paths, cleanup_temp_files
from .utils import get_port_deduplicator, performance_monitor
from .utils import handle_error, retry_with_backoff, safe_execute, ScanError
from .scanning import run_naabu_scan, run_httpx_probe, run_nmap_batch_scan
from .notifications import send_discord_notification, send_digest_notification, send_post, close_session

colorama_init()

# Flag for config reload
_RELOAD_CONFIG_FLAG = False

def _handle_sighup(_signum, _frame):
    """Signal handler for SIGHUP to trigger config reload."""
    global _RELOAD_CONFIG_FLAG
    logger = logging.getLogger(__name__)
    logger.info(f"{Fore.MAGENTA}SIGHUP received. Marking configuration for reload.{Style.RESET_ALL}")
    _RELOAD_CONFIG_FLAG = True

def notify_error(webhook_url: str, message: str):
    """
    Helper function to send error notifications via Discord.
    
    Args:
        webhook_url: Discord webhook URL
        message: Message to send
    """
    if not webhook_url:
        return
        
    try:
        send_post(webhook_url, json_data={"content": message}, timeout=10)
    except Exception as discord_e:
        logger = logging.getLogger(__name__)
        logger.error(f"{Fore.RED}[-] Failed to send Discord error notification: {discord_e}{Style.RESET_ALL}")

@performance_monitor
def process_target(config: dict, target: str, scan_mode: str = 'regular') -> bool:
    """
    Optimized target processing with performance monitoring and error handling.
    """
    logger = logging.getLogger(__name__)
    scan_type_log = f"[{scan_mode.replace('_', ' ').title()}]"
    logger.info(f"{Fore.CYAN}{scan_type_log} Processing target: {target}{Style.RESET_ALL}")

    try:
        # Get config settings used frequently
        notification_mode = config.get('notification_mode', 'digest')
        critical_ports_config = config.get('critical_ports', [])
        critical_ports_set = set(critical_ports_config)
        nmap_enabled = config.get('nmap_scan_enabled', False)

        # Get target paths with error handling
        paths = safe_execute(get_target_paths, config, target, default_return=None)
        if not paths:
            raise ScanError(f"Could not get paths for target '{target}'")

        # Load previous state with error handling
        previous_ports = safe_execute(load_previous_state, paths['state_file'], default_return={})

    except Exception as e:
        handle_error(e, {'target': target, 'scan_mode': scan_mode})
        logger.error(f"{Fore.RED}{scan_type_log} Error initializing target '{target}': {e}{Style.RESET_ALL}")
        return False
    
    # Optimized scanning approach: combine ports intelligently to avoid duplicate scans
    custom_ports_config = config.get('custom_ports', [])
    top_ports_count = config.get('top_ports', 1000)

    # Optimized scanning with error handling and retry logic
    try:
        # Check cache first for performance optimization
        _ = get_port_deduplicator()  # Initialize deduplicator for performance

        # Determine scanning strategy based on scan mode and configuration
        if scan_mode == 'daily_top_1000':
            # For daily scans, always scan top 1000
            logger.info(f"{Fore.YELLOW}[Daily Top 1000] Running comprehensive top {top_ports_count} scan for {target}...{Style.RESET_ALL}")
            scan_config = copy.deepcopy(config)
            scan_config['top_ports'] = top_ports_count
            scan_config['custom_ports'] = []

            @retry_with_backoff(max_attempts=2, base_delay=5.0)
            def scan_daily():
                return run_naabu_scan(scan_config, paths, target, scan_mode='daily_top_1000')

            current_ports = scan_daily()

        elif custom_ports_config:
            # For regular scans with custom ports, combine with top ports to avoid duplicates
            logger.info(f"{Fore.YELLOW}[Smart Scan] Running optimized scan combining top {top_ports_count} + {len(custom_ports_config)} custom ports for {target}...{Style.RESET_ALL}")

            @retry_with_backoff(max_attempts=2, base_delay=3.0)
            def scan_combined():
                return run_naabu_scan(config, paths, target, scan_mode='combined')

            current_ports = scan_combined()

        else:
            # Fallback to top ports only
            logger.info(f"{Fore.YELLOW}[Top Ports] Running top {top_ports_count} ports scan for {target}...{Style.RESET_ALL}")

            @retry_with_backoff(max_attempts=2, base_delay=3.0)
            def scan_top_ports():
                return run_naabu_scan(config, paths, target, scan_mode='top_ports')

            current_ports = scan_top_ports()

        # Handle scan failure
        if current_ports is None:
            raise ScanError(f"Naabu scan failed for '{target}' after retries")

        logger.info(f"{Fore.GREEN}[Scan Complete] Found {len(current_ports)} total ports for {target}.{Style.RESET_ALL}")

    except Exception as e:
        handle_error(e, {'target': target, 'scan_mode': scan_mode, 'operation': 'port_scanning'})
        logger.error(f"{Fore.RED}[Scan Failed] Error scanning '{target}': {e}{Style.RESET_ALL}")
        current_ports = set()  # Continue with empty set to avoid breaking the workflow
    
    if current_ports is None or len(current_ports) == 0:
        logger.error(f"{Fore.RED}{scan_type_log} No ports found for '{target}'. Skipping state update.{Style.RESET_ALL}")
        return False

    # Use the normalize_ports helper to handle both dict and set formats
    previous_ports_set: Set[str] = normalize_ports(previous_ports)
    added_ports: Set[str] = current_ports - previous_ports_set
    removed_ports: Set[str] = previous_ports_set - current_ports

    if not added_ports and not removed_ports:
        logger.info(f"{Fore.BLUE}{scan_type_log} No port changes detected for {target}.{Style.RESET_ALL} {Fore.BLUE}😴{Style.RESET_ALL}")
        return True # Skip rest of processing and saving state

    changes_made = False # Flag to track if state needs saving

    # --- Separate critical ports from normal new ports ---
    critical_hits: Set[str] = set()
    normal_hits: Set[str] = set()
    if added_ports:
        changes_made = True # Mark changes occurred
        for host_port in added_ports:
            try:
                port_str = host_port.split(':')[-1]
                port_int = int(port_str)
                if port_int in critical_ports_set:
                    critical_hits.add(host_port)
                else:
                    normal_hits.add(host_port)
            except (ValueError, IndexError) as e: # Handle potential bad host:port format from Naabu
                logger.warning(f"Could not parse port from Naabu output '{host_port}', treating as normal: {e}")
                normal_hits.add(host_port)
        logger.info(f"{Fore.GREEN}{scan_type_log} {len(added_ports)} New ports for {target}: "
                    f"{Fore.RED}{len(critical_hits)} Critical{Style.RESET_ALL}, "
                    f"{Fore.GREEN}{len(normal_hits)} Normal{Style.RESET_ALL}. {Fore.GREEN}🚀{Style.RESET_ALL}")

    # --- Optimized Service Discovery ---
    service_details = {}

    if nmap_enabled and added_ports:  # Only scan NEW ports for efficiency
        logger.info(f"{Fore.YELLOW}[Nmap] Running Nmap service scan on {len(added_ports)} new port(s)...{Style.RESET_ALL}")

        # Group new ports by host for efficient batch scanning
        host_to_ports = defaultdict(list)
        for host_port in added_ports:
            try:
                host, port = host_port.split(':', 1)
                host_to_ports[host].append(port)
            except ValueError:
                logger.warning(f"{Fore.YELLOW}[Nmap] Skipping invalid host:port format: '{host_port}'{Style.RESET_ALL}")

        # Track scanning statistics
        successful_hosts = 0
        failed_hosts = 0
        total_ports_scanned = 0

        # Perform optimized batch scanning for each host
        for host, ports in host_to_ports.items():
            if not ports:
                continue

            logger.debug(f"[Nmap] Scanning {len(ports)} new ports on host {host}")
            try:
                # Use batch scanning for efficiency
                nmap_results = run_nmap_batch_scan(config, host, ports)
                if nmap_results:
                    for port, nmap_data in nmap_results.items():
                        if nmap_data:  # Only store valid results
                            host_port = f"{host}:{port}"
                            service_details[host_port] = nmap_data
                            total_ports_scanned += 1
                    successful_hosts += 1
                    logger.debug(f"[Nmap] Successfully scanned {len(nmap_results)} ports on {host}")
                else:
                    failed_hosts += 1
                    logger.warning(f"[Nmap] No results from batch scan for {host}")
            except Exception as e:
                logger.error(f"{Fore.RED}[Nmap] Error scanning {host}: {e}{Style.RESET_ALL}")
                failed_hosts += 1

        if successful_hosts > 0 or failed_hosts > 0:
            logger.info(f"{Fore.GREEN}[Nmap] Scan complete: {successful_hosts} hosts successful, {failed_hosts} failed. {len(service_details)} services identified.{Style.RESET_ALL}")
    elif nmap_enabled:
        logger.debug("[Nmap] No new ports to scan")

    # --- Process CRITICAL New Ports (Always Immediate Notification) ---
    if critical_hits:
        for host_port in sorted(list(critical_hits)):
            logger.warning(f"{Fore.RED}{Style.BRIGHT}[Critical] CRITICAL port open: {host_port} on {target}. Probing...{Style.RESET_ALL}")
            probe_results = run_httpx_probe(config, host_port)
            nmap_results = service_details.get(host_port) if nmap_enabled else None
            
            # Send immediate notification for critical ports
            send_discord_notification(config, target, host_port, probe_results, nmap_results, is_critical=True)

    # --- Process NORMAL New Ports (Respects notification_mode) ---
    digest_added_details: List[Dict[str, Any]] = [] # Collect details ONLY for digest mode
    if normal_hits:
        logger.info(f"{Fore.BLUE}[Normal] Processing {len(normal_hits)} normal new ports for {target}...{Style.RESET_ALL}")
        for host_port in sorted(list(normal_hits)):
            logger.info(f"{Fore.YELLOW}[Normal] Probing normal port {host_port} on {target}...{Style.RESET_ALL}")
            probe_results = run_httpx_probe(config, host_port)
            nmap_results = service_details.get(host_port) if nmap_enabled else None

            if notification_mode == 'immediate':
                # Send notification immediately
                send_discord_notification(config, target, host_port, probe_results, nmap_results, is_critical=False)
                logger.info(f"{Fore.GREEN}[Normal] Sent immediate notification for {host_port}{Style.RESET_ALL}")
            else: # 'digest' mode
                # Collect details for later summary
                digest_added_details.append({
                    "host_port": host_port,
                    "probe_results": probe_results,
                    "nmap_results": nmap_results # Store nmap results too
                })
                logger.debug(f"[Normal] Added {host_port} to digest for later notification")

    # --- Process REMOVED Ports (Only relevant for Digest Mode Notification) ---
    digest_removed_list: List[str] = []
    if removed_ports:
        changes_made = True # Mark changes occurred
        sorted_removed = sorted(list(removed_ports))
        logger.info(f"{Fore.RED}{scan_type_log} {len(removed_ports)} Closed ports for {target}: {Style.RESET_ALL}{Fore.RED}{sorted_removed}{Style.RESET_ALL} {Fore.RED}💨{Style.RESET_ALL}")
        if notification_mode == 'digest':
             digest_removed_list = sorted_removed # Store for digest

    # --- Send Digest Notification (if applicable and changes occurred) ---
    if notification_mode == 'digest' and (digest_added_details or digest_removed_list):
        logger.info(f"{scan_type_log} Sending digest notification for {target}...")
        send_digest_notification(config, target, scan_mode, digest_added_details, digest_removed_list)

    # Save the state if changes were made
    if changes_made:
        # Ensure consistent state format by converting current_ports to a dictionary if it isn't already
        if isinstance(current_ports, set):
            current_state = {port: {"first_seen": datetime.datetime.now().isoformat()} for port in current_ports}
        else:
            current_state = current_ports
            
        if save_current_state(paths['state_file'], current_state):
            logger.info(f"{scan_type_log} State file updated for {target} due to port changes.")
    else:
        # Should not be reached if checks above are correct, but for safety:
        logger.debug(f"{scan_type_log} No changes detected requiring state save for {target}.")
    
    # --- Clean up temporary files ---
    # Only clean if explicitly enabled in config
    if config.get('cleanup_temp_files', False):
        scan_output_dir = paths['scan_output_dir']
        logger.debug(f"{scan_type_log} Cleaning up temporary scan files for {target}...")
        files_cleaned, bytes_freed = cleanup_temp_files(scan_output_dir)
        if files_cleaned > 0:
            logger.info(f"{scan_type_log} Removed {files_cleaned} temporary files for {target}, freed {bytes_freed/1024:.1f}KB")
        
    return True


# --- run_daily_top_1000_scan (Keep as is) ---
def run_daily_top_1000_scan(config: dict) -> Tuple[int, int]:
    # ... (no changes needed here) ...
    logger = logging.getLogger(__name__)
    logger.info(f"{Fore.MAGENTA}[*] === Starting Daily Top 1000 Scan Cycle ==={Style.RESET_ALL} ✨")
    targets_to_process = config.get('targets', [])
    if not targets_to_process:
        logger.warning(f"{Fore.YELLOW}[!] No targets configured for daily scan.{Style.RESET_ALL}")
        return 0, 0

    success_count = 0
    error_count = 0
    start_time = time.time()
    for target in targets_to_process:
        try:
            if process_target(config, target, scan_mode='daily_top_1000'):
                success_count += 1
            else:
                error_count += 1
        except Exception as e:
            logger.error(f"{Fore.RED}[-] Unhandled error during daily scan for target '{target}': {e}{Style.RESET_ALL}", exc_info=True)
            error_count += 1
            webhook = config.get('discord_webhook_url')
            if webhook:
                notify_error(webhook, f"⚠️ **Portmon Error (Daily Scan)** for target `{target}`: ```{str(e)[:1800]}```")

    duration = time.time() - start_time
    logger.info(f"{Fore.MAGENTA}[*] === Daily Top 1000 Scan Cycle Complete ({success_count} succeeded, {error_count} errors in {duration:.1f}s) ==={Style.RESET_ALL} ✅")
    
    return success_count, error_count


# --- monitor_mode ---
def monitor_mode():
    """Main monitoring loop with dynamic config reload support."""
    global _RELOAD_CONFIG_FLAG
    setup_logging()
    logger = logging.getLogger(__name__)

    # Register signal handlers if available (SIGHUP not available on Windows)
    try:
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, _handle_sighup)
            logger.info("SIGHUP handler registered for dynamic configuration reload.")
        else:
            logger.warning("SIGHUP not available on this system. Config reload will use file modification detection.")
    except (AttributeError, OSError) as e:
        logger.warning(f"Could not register SIGHUP handler: {e}. Config reload will use file modification detection.")
        
    # Define a cleanup function for graceful shutdown
    def cleanup_resources():
        logger.info(f"{Fore.BLUE}Cleaning up resources before exit...{Style.RESET_ALL}")
        try:
            # Close the shared HTTP session
            if close_session():
                logger.info("Closed shared HTTP session.")
        except Exception as e:
            logger.error(f"Error closing resources: {e}")
    
    # Handle SIGTERM for graceful shutdown in Docker/container environments
    if hasattr(signal, 'SIGTERM'):
        def sigterm_handler(*_):  # Use underscore to indicate unused parameters
            logger.info(f"{Fore.YELLOW}SIGTERM received, cleaning up...{Style.RESET_ALL}")
            cleanup_resources()
            sys.exit(0)

        signal.signal(signal.SIGTERM, sigterm_handler)
        logger.info("SIGTERM handler registered for graceful shutdown.")

    if not os.path.exists(CONFIG_FILE):
        logger.critical(f"{Fore.RED}[-] Config file not found ({CONFIG_FILE}). Exiting.{Style.RESET_ALL}")
        sys.exit(1)

    cfg = None
    interval = 3600  # Default interval if initial load fails

    while True:
        try:
            # Check reload flag or let load_config check mtime
            should_force_reload = False
            if _RELOAD_CONFIG_FLAG:
                should_force_reload = True
                _RELOAD_CONFIG_FLAG = False
                logger.info(f"{Fore.MAGENTA}Reloading configuration due to SIGHUP...{Style.RESET_ALL}")

            # Load or reload config
            loaded_cfg = load_config(force_reload=should_force_reload)
            if loaded_cfg:
                cfg = loaded_cfg
                interval = cfg.get('scan_interval_seconds', 7200)
            elif cfg is None:
                logger.error("Failed to load configuration. Retrying after default interval...")
                interval = 3600
                time.sleep(interval)
                continue

            # --- Daily Scan Check ---
            today_str = date.today().isoformat()
            last_scan_date = get_last_daily_scan_date()
            if last_scan_date != today_str:
                logger.info(f"{Fore.MAGENTA}[*] Triggering Daily Top 1000 scan (Last: {last_scan_date}, Today: {today_str}){Style.RESET_ALL}")
                try:
                    run_daily_top_1000_scan(cfg)
                    update_last_daily_scan_date(today_str)
                except Exception as daily_e:
                    logger.error(f"{Fore.RED}[-] Error during Daily Top 1000 scan execution: {daily_e}{Style.RESET_ALL}", exc_info=True)
            else:
                logger.info(f"{Fore.BLUE}[i] Daily Top 1000 scan already done for {today_str}.{Style.RESET_ALL}")

            # --- Regular Scan Cycle ---
            logger.info(f"{Fore.BLUE}[i] === Starting Regular Scan Cycle ==={Style.RESET_ALL}")
            targets_to_process = cfg.get('targets', [])

            # Ensure targets_to_process is a list
            if not isinstance(targets_to_process, list):
                logger.error(f"Invalid targets configuration: expected list, got {type(targets_to_process)}")
                targets_to_process = []

            configured_workers = cfg.get('max_concurrent_targets', 5)

            # Ensure configured_workers is an integer
            if not isinstance(configured_workers, int):
                logger.warning(f"Invalid max_concurrent_targets: expected int, got {type(configured_workers)}. Using default 5.")
                configured_workers = 5

            # Honor global scan rate limit if set
            global_rate_limit = cfg.get('global_scan_rate_limit')
            if global_rate_limit is not None and isinstance(global_rate_limit, int):
                max_workers = min(configured_workers, global_rate_limit)
                logger.info(f"Using {max_workers} workers (limited by global_scan_rate_limit)")
            else:
                max_workers = configured_workers
                logger.info(f"Using {max_workers} workers (from max_concurrent_targets)")

            start_time = time.time()
            completed_count = 0

            success_count = 0
            error_count = 0

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_target = {
                    executor.submit(process_target, cfg, target, scan_mode='regular'): target 
                    for target in targets_to_process
                }

                for future in as_completed(future_to_target):
                    target = future_to_target[future]
                    completed_count += 1
                    try:
                        result = future.result()
                        logger.info(f"Finished regular processing for target: {target} ({completed_count}/{len(targets_to_process)})")
                        if result:
                            success_count += 1
                        else:
                            error_count += 1
                    except Exception as exc:
                        logger.error(f"{Fore.RED}[-] Error processing target '{target}' in regular scan: {exc}{Style.RESET_ALL}", exc_info=True)
                        error_count += 1
                        webhook_url = cfg.get('discord_webhook_url') if cfg else None
                        if webhook_url and isinstance(webhook_url, str):
                            msg = f"⚠️ **Portmon Error (Regular Scan)** processing target `{target}`: ```{str(exc)[:1800]}```"
                            notify_error(webhook_url, msg)

            duration = time.time() - start_time
            logger.info(f"{Fore.BLUE}[i] === Regular Scan Cycle Complete ({success_count} succeeded, {error_count} failed, {len(targets_to_process)} targets in {duration:.1f}s) ==={Style.RESET_ALL}")

        except KeyboardInterrupt:
            logger.info(f"\n{Fore.YELLOW}[!] Monitoring interrupted. Shutting down...{Style.RESET_ALL} 👋")
            cleanup_resources()
            break
        except Exception as e:
            logger.error(f"{Fore.RED}[-] Unexpected error in main loop: {e}{Style.RESET_ALL}", exc_info=True)
            interval = 3600  # Fallback interval
            webhook_url = cfg.get('discord_webhook_url') if cfg else None
            if webhook_url and isinstance(webhook_url, str):
                msg = f"⚠️ **Portmon Global Error**: ```{str(e)[:1800]}```"
                notify_error(webhook_url, msg)

        # Ensure interval is a valid number for sleep
        if not isinstance(interval, (int, float)):
            logger.warning(f"Invalid interval type: {type(interval)}. Using default 3600 seconds.")
            interval = 3600

        logger.info(f"{Fore.BLUE}[i] === Scan cycles complete. Sleeping for {interval} seconds... ==={Style.RESET_ALL} ⏳")
        try:
            time.sleep(interval)
        except KeyboardInterrupt:
            logger.info(f"\n{Fore.YELLOW}[!] Sleep interrupted. Shutting down...{Style.RESET_ALL} 😴")
            cleanup_resources()
            break


# --- main function (Keep as is) ---
def main():
    parser = argparse.ArgumentParser(description="Port Monitor Tool using Naabu and Httpx.")
    subparsers = parser.add_subparsers(dest='command')
    
    # Monitor command (default)
    subparsers.add_parser('monitor', help='Run continuous monitoring mode')

    # One-shot scan command
    oneshot_parser = subparsers.add_parser('scan', help='Run a one-time scan of specified targets')
    oneshot_parser.add_argument('--target', '-t', help='Specific target to scan')
    
    args = parser.parse_args()
    
    # Default to monitor if no command specified
    if not args.command:
        monitor_mode()
    elif args.command == 'monitor':
        monitor_mode()
    elif args.command == 'scan':
        # Future one-shot scan functionality
        print("One-time scan not yet implemented")
    else:
        parser.print_help()

if __name__ == '__main__':
    main()