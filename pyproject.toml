# pyproject.toml

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "portmon"
version = "0.2.0"
description = "Advanced port monitoring tool with intelligent scanning, service detection, and Discord notifications"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
  "PyYAML>=5.4",        # For reading config.yaml
  "requests>=2.25",     # For sending Discord webhooks and HTTP operations
  "colorama>=0.4.4",    # For colored console output
  "pydantic>=1.8.0",    # For configuration validation and data models
]

[project.optional-dependencies]
dev = [
  "pytest>=6.0",
  "pytest-cov>=2.0",
  "black>=21.0",
  "flake8>=3.8",
  "mypy>=0.800"
]

[project.scripts]
portmon = "portmon.main:main"


[tool.setuptools.packages.find]
include = ["portmon*"]
# or, if you’d rather exclude the other package:
# exclude = ["portmon_output"]

[tool.setuptools.package-data]
portmon = ["py.typed"]

# Development tools configuration
[tool.black]
line-length = 100
target-version = ['py38']

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "--cov=portmon --cov-report=html --cov-report=term-missing"