# pyproject.toml

[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "portmon"
version = "0.1.0"
description = "Port monitoring tool using Naabu, Httpx, and Discord alerts"
readme = "README.md"
requires-python = ">=3.7"
dependencies = [
  "PyYAML>=5.4",      # For reading config.yaml
  "requests>=2.25",   # For sending Discord webhooks
  "colorama>=0.4.4"   # For colored console output
]

[project.scripts]
portmon = "portmon.main:main"


[tool.setuptools.packages.find]
include = ["portmon*"]
# or, if you’d rather exclude the other package:
# exclude = ["portmon_output"]