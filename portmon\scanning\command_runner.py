"""
Centralized command execution utility for portmon.

This module provides standardized subprocess execution with consistent error handling,
timeout management, and output processing for all external tool calls (naabu, httpx, nmap).
"""

import subprocess
import logging
import shlex
import os
from typing import List, Dict, Optional, Tuple
from pathlib import Path
from colorama import Fore, Style

logger = logging.getLogger(__name__)

def run_command(
    cmd: List[str],
    timeout: int = 300,
    description: str = "command",
    output_file: Optional[Path] = None,
    cwd: Optional[Path] = None,
    env: Optional[Dict[str, str]] = None,
    log_level: int = logging.INFO,
    output_log_level: int = logging.DEBUG,
    max_output_length: int = 1000,
    stream_output: bool = False
) -> Tuple[bool, Optional[str], Optional[str], Optional[Path]]:
    """
    Execute a subprocess command with standardized error handling and logging.

    Args:
        cmd: Command and arguments as a list
        timeout: Maximum execution time in seconds
        description: Human-readable description for logs
        output_file: Optional file path where command should write output
        cwd: Working directory for the command execution
        env: Environment variables to set for the command
        log_level: Logging level for command execution logs
        output_log_level: Logging level for command output
        max_output_length: Maximum length of stdout/stderr to log
        stream_output: Whether to stream output (for large outputs)

    Returns:
        Tuple of (success_bool, stdout, stderr, output_file_path)
        stdout/stderr will be None if output was redirected to file
    """
    logger.log(log_level, f"Running {description}: {shlex.join(cmd)}")

    # Prepare environment
    process_env = os.environ.copy()
    if env:
        process_env.update(env)

    # Set up output handling
    stdout_dest = subprocess.PIPE
    stderr_dest = subprocess.PIPE
    output_file_used = False
    file_handle = None

    try:
        if output_file:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            file_handle = output_file.open('wb')
            stdout_dest = file_handle
            stderr_dest = subprocess.STDOUT  # Combine stdout and stderr
            output_file_used = True

        if stream_output and not output_file_used:
            # Stream output for large command outputs, merge stderr into stdout to avoid deadlocks
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # Merge stderr into stdout to prevent deadlocks
                text=True,
                cwd=cwd,
                env=process_env,
                encoding='utf-8',
                errors='ignore'
            )
            
            output_lines = []
            
            # Stream combined output
            if process.stdout:
                for line in process.stdout:
                    line = line.rstrip()
                    output_lines.append(line)
                    logger.log(output_log_level, f"[{description}] {line}")
            
            # Wait for completion with timeout
            returncode = process.wait(timeout=timeout)
            stdout = '\n'.join(output_lines) if output_lines else ""
            stderr = ""  # We merged stderr into stdout
            success = returncode == 0

        else:
            # Capture output directly
            result = subprocess.run(
                cmd,
                stdout=stdout_dest,
                stderr=stderr_dest,
                check=False,
                timeout=timeout,
                cwd=cwd,
                env=process_env,
                text=not output_file_used,  # Text mode only if not writing to file
                encoding='utf-8',
                errors='ignore'
            )

            if output_file_used:
                stdout = None
                stderr = None
                success = result.returncode == 0
            else:
                stdout = result.stdout.strip() if result.stdout else ""
                stderr = result.stderr.strip() if result.stderr else ""
                success = result.returncode == 0

        # Consolidated logging in one place for both execution paths
        returncode = -1
        if 'result' in locals():
            returncode = result.returncode
        elif 'process' in locals():
            returncode = process.returncode
            
        if success:
            logger.log(log_level, f"{Fore.GREEN}\u2713 {description} completed successfully{Style.RESET_ALL}")
            
            # Only log standard output if we captured it and it's not empty
            if stdout and not output_file_used:
                # Truncate long outputs for logging
                stdout_logged = stdout[:max_output_length] + "... (truncated)" if len(stdout) > max_output_length else stdout
                logger.log(output_log_level, f"Output:\n{stdout_logged}")
        else:
            logger.error(f"{Fore.RED}\u2717 {description} failed with code {returncode}{Style.RESET_ALL}")
            
            # Log error output if available
            if stderr and not output_file_used:
                stderr_logged = stderr[:max_output_length] + "... (truncated)" if len(stderr) > max_output_length else stderr
                logger.error(f"Error output:\n{stderr_logged}")
                
            # Also log standard output on failure if available
            if stdout and not output_file_used:
                stdout_logged = stdout[:max_output_length] + "... (truncated)" if len(stdout) > max_output_length else stdout
                logger.log(output_log_level, f"Standard output:\n{stdout_logged}")

        # Check if output file exists when expected
        if output_file and not output_file.exists() and success:
            logger.warning(f"{Fore.YELLOW}! {description} returned success but output file {output_file} was not created{Style.RESET_ALL}")

        return success, stdout, stderr, output_file if output_file_used else None

    except subprocess.TimeoutExpired:
        logger.error(f"{Fore.RED}\u2717 {description} timed out after {timeout}s{Style.RESET_ALL}")
        return False, "", f"Command timed out after {timeout}s", None
    except FileNotFoundError:
        error_msg = f"Command not found: '{cmd[0]}'"
        logger.critical(f"{Fore.RED}\u2717 {error_msg}{Style.RESET_ALL}")
        return False, "", error_msg, None
    except Exception as e:
        error_msg = f"Unexpected error: {str(e)}"
        logger.error(f"{Fore.RED}\u2717 Unexpected error running {description}: {e}{Style.RESET_ALL}", exc_info=True)
        return False, "", error_msg, None
    finally:
        # Ensure file handle is closed
        if file_handle:
            file_handle.close()

def parse_line_based_output(output_file: Optional[Path] = None,
                           output: Optional[str] = None,
                           encoding: str = 'utf-8') -> List[str]:
    """
    Parse a line-based output from file or string and return lines as a list.
    Either output_file or output must be provided.

    Args:
        output_file: Path to the output file (optional)
        output: String output to parse (optional)
        encoding: Character encoding to use when reading the file

    Returns:
        List of non-empty, stripped lines

    Raises:
        ValueError: If neither output_file nor output is provided
        IOError: If file cannot be read due to permissions or other IO issues
    """
    if output_file is None and output is None:
        raise ValueError("Either output_file or output must be provided")
        
    try:
        # Parse from string if provided
        if output is not None:
            return [line.strip() for line in output.splitlines() if line.strip()]
            
        # Otherwise parse from file - Check existence first
        if not output_file.exists():
            logger.warning(f"Output file does not exist: {output_file}")
            return []
        
        # Let IOError propagate to caller after logging
        content = output_file.read_text(encoding=encoding, errors='ignore')
        return [line.strip() for line in content.splitlines() if line.strip()]

    except (UnicodeError, IOError) as e:
        logger.error(f"Error reading output: {e}", exc_info=True)
        return []
    except Exception as e:
        # Log but re-raise other unexpected errors
        logger.error(f"Unexpected error parsing output: {e}", exc_info=True)
        raise
