# File: portmon/utils/security.py
# Description: Security utilities and input sanitization for portmon

import re
import os
import logging
import hashlib
import secrets
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
from urllib.parse import urlparse
import ipaddress

logger = logging.getLogger(__name__)

class SecurityValidator:
    """
    Comprehensive security validation and sanitization utilities.
    """
    
    # Dangerous characters and patterns
    DANGEROUS_CHARS = ['..', '/', '\\', '|', '&', ';', '$', '`', '(', ')', '{', '}', '[', ']', '<', '>', '"', "'"]
    SHELL_METACHARACTERS = ['|', '&', ';', '$', '`', '\\', '"', "'", ' ', '\t', '\n', '*', '?', '[', ']', '{', '}', '~']
    
    @staticmethod
    def sanitize_filename(filename: str, max_length: int = 255) -> str:
        """
        Sanitize a filename to prevent path traversal and other attacks.
        
        Args:
            filename: Original filename
            max_length: Maximum allowed length
            
        Returns:
            Sanitized filename
        """
        if not filename:
            return "unnamed_file"
        
        # Remove path components
        filename = os.path.basename(filename)
        
        # Replace dangerous characters
        sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
        
        # Remove control characters
        sanitized = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', sanitized)
        
        # Ensure it doesn't start with dots or spaces
        sanitized = sanitized.lstrip('. ')
        
        # Truncate if too long
        if len(sanitized) > max_length:
            name, ext = os.path.splitext(sanitized)
            max_name_length = max_length - len(ext)
            sanitized = name[:max_name_length] + ext
        
        # Ensure it's not empty after sanitization
        if not sanitized:
            sanitized = "sanitized_file"
        
        return sanitized
    
    @staticmethod
    def sanitize_path(path: Union[str, Path], base_dir: Optional[Path] = None) -> Optional[Path]:
        """
        Sanitize a file path to prevent directory traversal attacks.
        
        Args:
            path: Path to sanitize
            base_dir: Base directory to restrict access to
            
        Returns:
            Sanitized path or None if invalid
        """
        try:
            # Convert to Path object
            if isinstance(path, str):
                path = Path(path)
            
            # Resolve to absolute path
            resolved_path = path.resolve()
            
            # Check for directory traversal
            if base_dir:
                base_dir = base_dir.resolve()
                try:
                    resolved_path.relative_to(base_dir)
                except ValueError:
                    logger.warning(f"Path traversal attempt detected: {path}")
                    return None
            
            # Check for dangerous patterns
            path_str = str(resolved_path)
            for dangerous in SecurityValidator.DANGEROUS_CHARS:
                if dangerous in path_str and dangerous not in ['/', '\\']:
                    logger.warning(f"Dangerous character in path: {dangerous}")
                    return None
            
            return resolved_path
            
        except Exception as e:
            logger.error(f"Path sanitization failed: {e}")
            return None
    
    @staticmethod
    def validate_domain(domain: str) -> bool:
        """
        Validate domain name format and check for suspicious patterns.
        
        Args:
            domain: Domain name to validate
            
        Returns:
            True if domain is valid and safe
        """
        if not domain or not isinstance(domain, str):
            return False
        
        # Basic length check
        if len(domain) > 253:
            return False
        
        # Check for suspicious patterns
        suspicious_patterns = [
            r'\.\.+',  # Multiple consecutive dots
            r'^\.|\.$',  # Starting or ending with dot
            r'[^a-zA-Z0-9.-]',  # Invalid characters
            r'--',  # Double hyphens (except in IDN)
            r'^-|-$',  # Starting or ending with hyphen
        ]
        
        for pattern in suspicious_patterns:
            if re.search(pattern, domain):
                return False
        
        # Validate each label
        labels = domain.split('.')
        for label in labels:
            if not label or len(label) > 63:
                return False
            if not re.match(r'^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?$', label):
                return False
        
        return True
    
    @staticmethod
    def validate_ip_address(ip: str) -> bool:
        """
        Validate IP address and check for private/reserved ranges.
        
        Args:
            ip: IP address to validate
            
        Returns:
            True if IP is valid and not in restricted ranges
        """
        try:
            ip_obj = ipaddress.ip_address(ip)
            
            # Check for private/reserved addresses
            if ip_obj.is_private or ip_obj.is_reserved or ip_obj.is_loopback:
                logger.warning(f"Private/reserved IP address: {ip}")
                return False
            
            return True
            
        except ValueError:
            return False
    
    @staticmethod
    def validate_url(url: str, allowed_schemes: List[str] = None) -> bool:
        """
        Validate URL format and scheme.
        
        Args:
            url: URL to validate
            allowed_schemes: List of allowed schemes (default: http, https)
            
        Returns:
            True if URL is valid and safe
        """
        if not url or not isinstance(url, str):
            return False
        
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        try:
            parsed = urlparse(url)
            
            # Check scheme
            if parsed.scheme not in allowed_schemes:
                return False
            
            # Check hostname
            if not parsed.hostname:
                return False
            
            # Validate hostname
            if not SecurityValidator.validate_domain(parsed.hostname):
                # Try as IP address
                if not SecurityValidator.validate_ip_address(parsed.hostname):
                    return False
            
            # Check for suspicious patterns in path
            if parsed.path and '..' in parsed.path:
                return False
            
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def sanitize_command_arg(arg: str) -> str:
        """
        Sanitize command line argument to prevent injection attacks.
        
        Args:
            arg: Command argument to sanitize
            
        Returns:
            Sanitized argument
        """
        if not arg:
            return ""
        
        # Remove shell metacharacters
        sanitized = arg
        for char in SecurityValidator.SHELL_METACHARACTERS:
            sanitized = sanitized.replace(char, '')
        
        # Limit length
        if len(sanitized) > 1000:
            sanitized = sanitized[:1000]
        
        return sanitized
    
    @staticmethod
    def validate_port(port: Union[int, str]) -> bool:
        """
        Validate port number.
        
        Args:
            port: Port number to validate
            
        Returns:
            True if port is valid
        """
        try:
            port_num = int(port)
            return 1 <= port_num <= 65535
        except (ValueError, TypeError):
            return False
    
    @staticmethod
    def validate_port_list(ports: List[Union[int, str]]) -> List[int]:
        """
        Validate and filter a list of ports.
        
        Args:
            ports: List of ports to validate
            
        Returns:
            List of valid port numbers
        """
        valid_ports = []
        for port in ports:
            if SecurityValidator.validate_port(port):
                valid_ports.append(int(port))
            else:
                logger.warning(f"Invalid port number: {port}")
        
        return valid_ports
    
    @staticmethod
    def generate_safe_filename(prefix: str = "portmon", extension: str = "txt") -> str:
        """
        Generate a safe, unique filename.
        
        Args:
            prefix: Filename prefix
            extension: File extension
            
        Returns:
            Safe filename
        """
        # Sanitize prefix
        safe_prefix = SecurityValidator.sanitize_filename(prefix)
        
        # Generate random suffix
        random_suffix = secrets.token_hex(8)
        
        # Combine with timestamp
        import time
        timestamp = int(time.time())
        
        return f"{safe_prefix}_{timestamp}_{random_suffix}.{extension}"
    
    @staticmethod
    def hash_sensitive_data(data: str, salt: Optional[str] = None) -> str:
        """
        Hash sensitive data for logging or storage.
        
        Args:
            data: Data to hash
            salt: Optional salt (generated if not provided)
            
        Returns:
            Hashed data
        """
        if salt is None:
            salt = secrets.token_hex(16)
        
        # Combine data and salt
        salted_data = f"{data}{salt}"
        
        # Hash with SHA-256
        hash_obj = hashlib.sha256(salted_data.encode('utf-8'))
        return hash_obj.hexdigest()
    
    @staticmethod
    def check_file_permissions(file_path: Path) -> Dict[str, Any]:
        """
        Check file permissions and security.
        
        Args:
            file_path: Path to check
            
        Returns:
            Dictionary with permission information
        """
        try:
            stat_info = file_path.stat()
            
            # Get permissions
            permissions = oct(stat_info.st_mode)[-3:]
            
            # Check for world-writable
            world_writable = bool(stat_info.st_mode & 0o002)
            
            # Check for group-writable
            group_writable = bool(stat_info.st_mode & 0o020)
            
            # Check owner
            owner_uid = stat_info.st_uid
            current_uid = os.getuid() if hasattr(os, 'getuid') else None
            
            return {
                "permissions": permissions,
                "world_writable": world_writable,
                "group_writable": group_writable,
                "owner_uid": owner_uid,
                "current_uid": current_uid,
                "is_owner": owner_uid == current_uid if current_uid is not None else None,
                "secure": not world_writable and not group_writable
            }
            
        except Exception as e:
            logger.error(f"Failed to check file permissions: {e}")
            return {"error": str(e)}

class InputValidator:
    """
    Specialized input validation for portmon operations.
    """
    
    @staticmethod
    def validate_target_list(targets: List[str]) -> List[str]:
        """
        Validate and filter a list of targets.
        
        Args:
            targets: List of target domains/IPs
            
        Returns:
            List of valid targets
        """
        valid_targets = []
        
        for target in targets:
            if not isinstance(target, str):
                logger.warning(f"Invalid target type: {type(target)}")
                continue
            
            target = target.strip()
            if not target:
                continue
            
            # Validate as domain or IP
            if SecurityValidator.validate_domain(target) or SecurityValidator.validate_ip_address(target):
                valid_targets.append(target)
            else:
                logger.warning(f"Invalid target format: {target}")
        
        return valid_targets
    
    @staticmethod
    def validate_scan_config(config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate scanning configuration for security issues.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Validated configuration
        """
        validated = {}
        
        # Validate numeric values
        numeric_fields = {
            'naabu_concurrency': (1, 1000),
            'naabu_rate': (0, 10000),
            'httpx_threads': (1, 500),
            'probe_timeout': (1, 300),
            'nmap_timeout_seconds': (30, 3600),
            'top_ports': (1, 65535)
        }
        
        for field, (min_val, max_val) in numeric_fields.items():
            if field in config:
                try:
                    value = int(config[field])
                    if min_val <= value <= max_val:
                        validated[field] = value
                    else:
                        logger.warning(f"Value for {field} out of range: {value}")
                        validated[field] = max(min_val, min(value, max_val))
                except (ValueError, TypeError):
                    logger.warning(f"Invalid numeric value for {field}: {config[field]}")
        
        # Validate port lists
        port_fields = ['custom_ports', 'exclude_ports', 'common_https_ports']
        for field in port_fields:
            if field in config and isinstance(config[field], list):
                validated[field] = SecurityValidator.validate_port_list(config[field])
        
        # Validate paths
        path_fields = ['naabu_path', 'httpx_path', 'nmap_path', 'recon_base_dir']
        for field in path_fields:
            if field in config and config[field]:
                # Basic path validation
                path_str = str(config[field])
                if not any(dangerous in path_str for dangerous in ['..', '|', '&', ';']):
                    validated[field] = path_str
                else:
                    logger.warning(f"Potentially dangerous path in {field}: {path_str}")
        
        # Validate URLs
        if 'discord_webhook_url' in config and config['discord_webhook_url']:
            if SecurityValidator.validate_url(config['discord_webhook_url'], ['https']):
                validated['discord_webhook_url'] = config['discord_webhook_url']
            else:
                logger.warning("Invalid Discord webhook URL")
        
        # Copy safe string values
        string_fields = ['log_level']
        for field in string_fields:
            if field in config and isinstance(config[field], str):
                validated[field] = config[field]
        
        # Copy boolean values
        bool_fields = ['nmap_scan_enabled', 'enable_json_logging', 'cleanup_temp_files']
        for field in bool_fields:
            if field in config and isinstance(config[field], bool):
                validated[field] = config[field]
        
        return validated

# Global security validator instance
_security_validator = SecurityValidator()

def get_security_validator() -> SecurityValidator:
    """Get the global security validator instance."""
    return _security_validator
