# File: portmon/utils/data_validator.py
# Description: Data validation and schema checking utilities

import json
import logging
from typing import Any, Dict, List, Optional, Union, Type, Callable
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class ValidationRule:
    """Represents a validation rule for data fields."""
    field_name: str
    required: bool = True
    data_type: Optional[Type] = None
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    min_length: Optional[int] = None
    max_length: Optional[int] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None
    custom_validator: Optional[Callable[[Any], bool]] = None
    description: str = ""

@dataclass
class ValidationResult:
    """Result of data validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    field_results: Dict[str, bool] = field(default_factory=dict)

class DataValidator:
    """
    Comprehensive data validation framework with schema support.
    """
    
    def __init__(self):
        self.schemas: Dict[str, List[ValidationRule]] = {}
        self._load_default_schemas()
    
    def _load_default_schemas(self) -> None:
        """Load default validation schemas for portmon data structures."""
        
        # Target validation schema
        self.schemas['target'] = [
            ValidationRule(
                field_name='name',
                required=True,
                data_type=str,
                min_length=1,
                max_length=253,
                description='Target domain or IP address'
            ),
            ValidationRule(
                field_name='type',
                required=False,
                data_type=str,
                allowed_values=['domain', 'ip', 'cidr'],
                description='Target type'
            )
        ]
        
        # Port scan result schema
        self.schemas['port_scan_result'] = [
            ValidationRule(
                field_name='target',
                required=True,
                data_type=str,
                min_length=1,
                description='Target that was scanned'
            ),
            ValidationRule(
                field_name='ports',
                required=True,
                data_type=list,
                description='List of discovered ports'
            ),
            ValidationRule(
                field_name='scan_time',
                required=True,
                data_type=(int, float),
                min_value=0,
                description='Scan duration in seconds'
            ),
            ValidationRule(
                field_name='timestamp',
                required=True,
                data_type=(str, datetime),
                description='Scan timestamp'
            )
        ]
        
        # HTTP probe result schema
        self.schemas['http_probe_result'] = [
            ValidationRule(
                field_name='url',
                required=True,
                data_type=str,
                min_length=1,
                description='Probed URL'
            ),
            ValidationRule(
                field_name='status_code',
                required=False,
                data_type=int,
                min_value=100,
                max_value=599,
                description='HTTP status code'
            ),
            ValidationRule(
                field_name='title',
                required=False,
                data_type=str,
                max_length=1000,
                description='Page title'
            ),
            ValidationRule(
                field_name='tech',
                required=False,
                data_type=list,
                description='Detected technologies'
            )
        ]
        
        # Configuration schema
        self.schemas['config'] = [
            ValidationRule(
                field_name='recon_base_dir',
                required=True,
                data_type=str,
                min_length=1,
                description='Base directory for reconnaissance data'
            ),
            ValidationRule(
                field_name='targets',
                required=True,
                data_type=list,
                min_length=1,
                description='List of targets to scan'
            ),
            ValidationRule(
                field_name='naabu_concurrency',
                required=False,
                data_type=int,
                min_value=1,
                max_value=1000,
                description='Naabu concurrency level'
            ),
            ValidationRule(
                field_name='httpx_threads',
                required=False,
                data_type=int,
                min_value=1,
                max_value=500,
                description='Httpx thread count'
            )
        ]
    
    def add_schema(self, schema_name: str, rules: List[ValidationRule]) -> None:
        """
        Add a custom validation schema.
        
        Args:
            schema_name: Name of the schema
            rules: List of validation rules
        """
        self.schemas[schema_name] = rules
        logger.debug(f"Added validation schema: {schema_name}")
    
    def validate_data(self, data: Dict[str, Any], schema_name: str) -> ValidationResult:
        """
        Validate data against a schema.
        
        Args:
            data: Data to validate
            schema_name: Name of the schema to use
            
        Returns:
            Validation result
        """
        if schema_name not in self.schemas:
            return ValidationResult(
                is_valid=False,
                errors=[f"Unknown schema: {schema_name}"]
            )
        
        result = ValidationResult(is_valid=True)
        rules = self.schemas[schema_name]
        
        # Check each rule
        for rule in rules:
            field_valid = self._validate_field(data, rule, result)
            result.field_results[rule.field_name] = field_valid
            
            if not field_valid:
                result.is_valid = False
        
        return result
    
    def _validate_field(self, data: Dict[str, Any], rule: ValidationRule, result: ValidationResult) -> bool:
        """
        Validate a single field against a rule.
        
        Args:
            data: Data dictionary
            rule: Validation rule
            result: Result object to update
            
        Returns:
            True if field is valid
        """
        field_name = rule.field_name
        
        # Check if field exists
        if field_name not in data:
            if rule.required:
                result.errors.append(f"Required field missing: {field_name}")
                return False
            else:
                return True  # Optional field not present is OK
        
        value = data[field_name]
        
        # Check data type
        if rule.data_type is not None:
            if not isinstance(value, rule.data_type):
                result.errors.append(f"Invalid type for {field_name}: expected {rule.data_type}, got {type(value)}")
                return False
        
        # Check value range for numbers
        if isinstance(value, (int, float)):
            if rule.min_value is not None and value < rule.min_value:
                result.errors.append(f"Value for {field_name} ({value}) below minimum ({rule.min_value})")
                return False
            if rule.max_value is not None and value > rule.max_value:
                result.errors.append(f"Value for {field_name} ({value}) above maximum ({rule.max_value})")
                return False
        
        # Check length for strings and lists
        if hasattr(value, '__len__'):
            length = len(value)
            if rule.min_length is not None and length < rule.min_length:
                result.errors.append(f"Length of {field_name} ({length}) below minimum ({rule.min_length})")
                return False
            if rule.max_length is not None and length > rule.max_length:
                result.errors.append(f"Length of {field_name} ({length}) above maximum ({rule.max_length})")
                return False
        
        # Check allowed values
        if rule.allowed_values is not None and value not in rule.allowed_values:
            result.errors.append(f"Invalid value for {field_name}: {value} not in {rule.allowed_values}")
            return False
        
        # Check pattern for strings
        if rule.pattern is not None and isinstance(value, str):
            import re
            if not re.match(rule.pattern, value):
                result.errors.append(f"Value for {field_name} does not match pattern: {rule.pattern}")
                return False
        
        # Custom validation
        if rule.custom_validator is not None:
            try:
                if not rule.custom_validator(value):
                    result.errors.append(f"Custom validation failed for {field_name}")
                    return False
            except Exception as e:
                result.errors.append(f"Custom validation error for {field_name}: {e}")
                return False
        
        return True
    
    def validate_json_file(self, file_path: Path, schema_name: str) -> ValidationResult:
        """
        Validate a JSON file against a schema.
        
        Args:
            file_path: Path to JSON file
            schema_name: Schema to validate against
            
        Returns:
            Validation result
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return self.validate_data(data, schema_name)
            
        except json.JSONDecodeError as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Invalid JSON in {file_path}: {e}"]
            )
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"Error reading {file_path}: {e}"]
            )
    
    def validate_port_list(self, ports: List[Union[int, str]]) -> ValidationResult:
        """
        Validate a list of port numbers.
        
        Args:
            ports: List of ports to validate
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if not isinstance(ports, list):
            result.is_valid = False
            result.errors.append("Ports must be a list")
            return result
        
        for i, port in enumerate(ports):
            try:
                port_num = int(port)
                if not (1 <= port_num <= 65535):
                    result.errors.append(f"Invalid port at index {i}: {port} (must be 1-65535)")
                    result.is_valid = False
            except (ValueError, TypeError):
                result.errors.append(f"Invalid port format at index {i}: {port}")
                result.is_valid = False
        
        return result
    
    def validate_target_list(self, targets: List[str]) -> ValidationResult:
        """
        Validate a list of targets.
        
        Args:
            targets: List of target domains/IPs
            
        Returns:
            Validation result
        """
        result = ValidationResult(is_valid=True)
        
        if not isinstance(targets, list):
            result.is_valid = False
            result.errors.append("Targets must be a list")
            return result
        
        if len(targets) == 0:
            result.is_valid = False
            result.errors.append("Target list cannot be empty")
            return result
        
        from .security import SecurityValidator
        
        for i, target in enumerate(targets):
            if not isinstance(target, str):
                result.errors.append(f"Invalid target type at index {i}: {type(target)}")
                result.is_valid = False
                continue
            
            target = target.strip()
            if not target:
                result.errors.append(f"Empty target at index {i}")
                result.is_valid = False
                continue
            
            # Validate format
            if not (SecurityValidator.validate_domain(target) or SecurityValidator.validate_ip_address(target)):
                result.errors.append(f"Invalid target format at index {i}: {target}")
                result.is_valid = False
        
        return result
    
    def get_schema_info(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a validation schema.
        
        Args:
            schema_name: Name of the schema
            
        Returns:
            Schema information or None if not found
        """
        if schema_name not in self.schemas:
            return None
        
        rules = self.schemas[schema_name]
        
        return {
            "name": schema_name,
            "rule_count": len(rules),
            "required_fields": [r.field_name for r in rules if r.required],
            "optional_fields": [r.field_name for r in rules if not r.required],
            "rules": [
                {
                    "field": r.field_name,
                    "required": r.required,
                    "type": r.data_type.__name__ if r.data_type else None,
                    "description": r.description
                }
                for r in rules
            ]
        }
    
    def list_schemas(self) -> List[str]:
        """
        Get list of available schemas.
        
        Returns:
            List of schema names
        """
        return list(self.schemas.keys())

# Global data validator instance
_data_validator = DataValidator()

def get_data_validator() -> DataValidator:
    """Get the global data validator instance."""
    return _data_validator

def validate_scan_result(result: Dict[str, Any]) -> ValidationResult:
    """
    Convenience function to validate scan results.
    
    Args:
        result: Scan result to validate
        
    Returns:
        Validation result
    """
    return _data_validator.validate_data(result, 'port_scan_result')

def validate_config(config: Dict[str, Any]) -> ValidationResult:
    """
    Convenience function to validate configuration.
    
    Args:
        config: Configuration to validate
        
    Returns:
        Validation result
    """
    return _data_validator.validate_data(config, 'config')
