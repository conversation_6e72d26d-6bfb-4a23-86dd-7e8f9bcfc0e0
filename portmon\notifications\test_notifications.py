#!/usr/bin/env python3
# File: portmon/notifications/test_notifications.py
# Description: Notification testing CLI tool

import sys
import argparse
import logging
from typing import Dict, Any

from .notifier import (
    get_notification_manager, 
    NotificationSeverity, 
    NotificationChannel,
    NotificationMetrics
)
from .http_utils import get_http_metrics

def setup_logging(verbose: bool = False) -> None:
    """Setup logging for the test tool."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s'
    )

def test_discord(webhook_url: str) -> bool:
    """Test Discord webhook notification."""
    print(f"🔍 Testing Discord webhook: {webhook_url[:50]}...")
    
    config = {'discord_webhook_url': webhook_url}
    manager = get_notification_manager()
    manager.configure(config)
    
    success = manager.send_notification(
        "Portmon Test Notification",
        "This is a test notification from the Portmon notification system.\n\n"
        "If you receive this message, your Discord webhook is configured correctly!",
        NotificationSeverity.LOW,
        config,
        [NotificationChannel.DISCORD]
    )
    
    if success:
        print("✅ Discord notification sent successfully!")
        return True
    else:
        print("❌ Discord notification failed!")
        return False

def test_email(smtp_config: Dict[str, Any]) -> bool:
    """Test email notification."""
    print(f"🔍 Testing email notification to: {smtp_config.get('to_emails', [])}")
    
    config = {'email': smtp_config}
    manager = get_notification_manager()
    manager.configure(config)
    
    success = manager.send_notification(
        "Portmon Test Notification",
        "This is a test notification from the Portmon notification system.\n\n"
        "If you receive this email, your email configuration is working correctly!",
        NotificationSeverity.LOW,
        config,
        [NotificationChannel.EMAIL]
    )
    
    if success:
        print("✅ Email notification sent successfully!")
        return True
    else:
        print("❌ Email notification failed!")
        return False

def test_all_channels(config: Dict[str, Any]) -> Dict[str, bool]:
    """Test all configured notification channels."""
    print("🔍 Testing all configured notification channels...")
    
    manager = get_notification_manager()
    manager.configure(config)
    
    results = manager.test_channels(config)
    
    print("\n📊 Test Results:")
    for channel, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  • {channel.title()}: {status}")
    
    return results

def show_metrics() -> None:
    """Display notification and HTTP metrics."""
    print("\n📈 Notification Metrics:")
    
    # Get notification metrics
    manager = get_notification_manager()
    metrics = manager.get_metrics()
    
    print(f"  • Total notifications sent: {metrics.total_sent}")
    print(f"  • Total notifications failed: {metrics.total_failed}")
    print(f"  • Success rate: {metrics.get_success_rate():.1f}%")
    print(f"  • Discord sent: {metrics.discord_sent}")
    print(f"  • Discord failed: {metrics.discord_failed}")
    print(f"  • Email sent: {metrics.email_sent}")
    print(f"  • Email failed: {metrics.email_failed}")
    
    if metrics.last_success:
        print(f"  • Last success: {metrics.last_success.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    if metrics.last_failure:
        print(f"  • Last failure: {metrics.last_failure.strftime('%Y-%m-%d %H:%M:%S UTC')}")
    
    # Get HTTP metrics
    http_metrics = get_http_metrics()
    print(f"\n🌐 HTTP Metrics:")
    print(f"  • Total requests: {http_metrics.total_requests}")
    print(f"  • Successful requests: {http_metrics.successful_requests}")
    print(f"  • Failed requests: {http_metrics.failed_requests}")
    print(f"  • Success rate: {http_metrics.get_success_rate():.1f}%")
    print(f"  • Average response time: {http_metrics.get_average_response_time():.2f}s")
    
    if http_metrics.last_request_time:
        print(f"  • Last request: {http_metrics.last_request_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")

def main():
    """Main entry point for the notification test tool."""
    parser = argparse.ArgumentParser(
        description="Portmon notification testing tool"
    )
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Discord test command
    discord_parser = subparsers.add_parser('discord', help='Test Discord webhook')
    discord_parser.add_argument('webhook_url', help='Discord webhook URL')
    
    # Email test command
    email_parser = subparsers.add_parser('email', help='Test email notification')
    email_parser.add_argument('--smtp-server', required=True, help='SMTP server hostname')
    email_parser.add_argument('--smtp-port', type=int, default=587, help='SMTP server port')
    email_parser.add_argument('--from-email', required=True, help='From email address')
    email_parser.add_argument('--to-emails', required=True, nargs='+', help='To email addresses')
    email_parser.add_argument('--username', help='SMTP username')
    email_parser.add_argument('--password', help='SMTP password')
    email_parser.add_argument('--no-tls', action='store_true', help='Disable TLS')
    
    # Test all command
    all_parser = subparsers.add_parser('all', help='Test all configured channels')
    all_parser.add_argument('config_file', help='Path to configuration file')
    
    # Metrics command
    subparsers.add_parser('metrics', help='Show notification metrics')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    setup_logging(args.verbose)
    
    try:
        if args.command == 'discord':
            success = test_discord(args.webhook_url)
            return 0 if success else 1
            
        elif args.command == 'email':
            smtp_config = {
                'smtp_server': args.smtp_server,
                'smtp_port': args.smtp_port,
                'from_email': args.from_email,
                'to_emails': args.to_emails,
                'use_tls': not args.no_tls
            }
            
            if args.username:
                smtp_config['username'] = args.username
            if args.password:
                smtp_config['password'] = args.password
            
            success = test_email(smtp_config)
            return 0 if success else 1
            
        elif args.command == 'all':
            # Load configuration file
            import yaml
            with open(args.config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            results = test_all_channels(config)
            all_passed = all(results.values())
            return 0 if all_passed else 1
            
        elif args.command == 'metrics':
            show_metrics()
            return 0
            
        else:
            print(f"Unknown command: {args.command}")
            return 1
            
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
