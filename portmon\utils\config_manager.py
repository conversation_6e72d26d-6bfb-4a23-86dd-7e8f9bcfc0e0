# File: portmon/utils/config_manager.py
# Description: Centralized configuration management and validation

import os
import logging
import yaml
import json
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timezone

from .error_handling import ConfigurationError, handle_error

logger = logging.getLogger(__name__)

@dataclass
class ConfigValidationResult:
    """Result of configuration validation."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    missing_keys: List[str] = field(default_factory=list)
    invalid_values: List[str] = field(default_factory=list)

class ConfigManager:
    """
    Centralized configuration management with validation and environment support.
    """
    
    def __init__(self, config_file: Optional[Path] = None):
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self._schema: Dict[str, Any] = {}
        self._env_prefix = "PORTMON_"
        self._load_default_schema()
    
    def _load_default_schema(self) -> None:
        """Load the default configuration schema."""
        self._schema = {
            # Required configuration keys
            'required': {
                'recon_base_dir': {'type': str, 'description': 'Base directory for reconnaissance data'},
                'targets': {'type': list, 'description': 'List of targets to scan'},
            },
            # Optional configuration keys with defaults
            'optional': {
                'naabu_path': {'type': str, 'default': 'naabu', 'description': 'Path to naabu binary'},
                'httpx_path': {'type': str, 'default': 'httpx', 'description': 'Path to httpx binary'},
                'nmap_path': {'type': str, 'default': 'nmap', 'description': 'Path to nmap binary'},
                'naabu_concurrency': {'type': int, 'default': 100, 'min': 1, 'max': 1000},
                'naabu_rate': {'type': int, 'default': 0, 'min': 0, 'max': 10000},
                'naabu_timeout_seconds': {'type': int, 'default': 5400, 'min': 60, 'max': 86400},
                'httpx_threads': {'type': int, 'default': 100, 'min': 1, 'max': 500},
                'probe_timeout': {'type': int, 'default': 15, 'min': 1, 'max': 300},
                'nmap_timeout_seconds': {'type': int, 'default': 300, 'min': 30, 'max': 3600},
                'top_ports': {'type': int, 'default': 1000, 'min': 1, 'max': 65535},
                'custom_ports': {'type': list, 'default': [], 'description': 'Custom ports to scan'},
                'exclude_ports': {'type': list, 'default': [], 'description': 'Ports to exclude'},
                'common_https_ports': {'type': list, 'default': [443, 8443, 9443], 'description': 'HTTPS ports'},
                'nmap_scan_enabled': {'type': bool, 'default': True, 'description': 'Enable Nmap service scanning'},
                'discord_webhook_url': {'type': str, 'default': '', 'description': 'Discord webhook URL'},
                'log_level': {'type': str, 'default': 'INFO', 'choices': ['DEBUG', 'INFO', 'WARNING', 'ERROR']},
                'enable_json_logging': {'type': bool, 'default': False, 'description': 'Enable JSON logging'},
                'cleanup_temp_files': {'type': bool, 'default': True, 'description': 'Clean up temporary files'},
            }
        }
    
    def load_config(self, config_file: Optional[Path] = None) -> Dict[str, Any]:
        """
        Load configuration from file with environment variable overrides.
        
        Args:
            config_file: Path to configuration file
            
        Returns:
            Loaded and validated configuration
        """
        if config_file:
            self.config_file = config_file
        
        # Start with defaults
        config = {}
        
        # Apply defaults from schema
        for key, spec in self._schema.get('optional', {}).items():
            if 'default' in spec:
                config[key] = spec['default']
        
        # Load from file if provided
        if self.config_file and self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.suffix.lower() in ['.yaml', '.yml']:
                        file_config = yaml.safe_load(f) or {}
                    elif self.config_file.suffix.lower() == '.json':
                        file_config = json.load(f) or {}
                    else:
                        raise ConfigurationError(f"Unsupported config file format: {self.config_file.suffix}")
                
                config.update(file_config)
                logger.info(f"Loaded configuration from {self.config_file}")
                
            except Exception as e:
                error_msg = f"Failed to load configuration from {self.config_file}: {e}"
                handle_error(ConfigurationError(error_msg))
                raise
        
        # Override with environment variables
        env_overrides = self._load_env_overrides()
        if env_overrides:
            config.update(env_overrides)
            logger.info(f"Applied {len(env_overrides)} environment variable overrides")
        
        # Validate configuration
        validation_result = self.validate_config(config)
        if not validation_result.is_valid:
            error_msg = f"Configuration validation failed: {', '.join(validation_result.errors)}"
            handle_error(ConfigurationError(error_msg, context={'validation': validation_result}))
            raise ConfigurationError(error_msg)
        
        # Log warnings
        for warning in validation_result.warnings:
            logger.warning(f"Configuration warning: {warning}")
        
        self._config = config
        return config
    
    def _load_env_overrides(self) -> Dict[str, Any]:
        """Load configuration overrides from environment variables."""
        overrides = {}
        
        for key in os.environ:
            if key.startswith(self._env_prefix):
                config_key = key[len(self._env_prefix):].lower()
                value = os.environ[key]
                
                # Try to parse as appropriate type
                try:
                    # Check if it's a boolean
                    if value.lower() in ['true', 'false']:
                        overrides[config_key] = value.lower() == 'true'
                    # Check if it's a number
                    elif value.isdigit():
                        overrides[config_key] = int(value)
                    # Check if it's a float
                    elif '.' in value and value.replace('.', '').isdigit():
                        overrides[config_key] = float(value)
                    # Check if it's a list (comma-separated)
                    elif ',' in value:
                        overrides[config_key] = [item.strip() for item in value.split(',')]
                    else:
                        overrides[config_key] = value
                        
                    logger.debug(f"Environment override: {config_key} = {overrides[config_key]}")
                    
                except Exception as e:
                    logger.warning(f"Failed to parse environment variable {key}: {e}")
        
        return overrides
    
    def validate_config(self, config: Dict[str, Any]) -> ConfigValidationResult:
        """
        Validate configuration against schema.
        
        Args:
            config: Configuration to validate
            
        Returns:
            Validation result with errors and warnings
        """
        result = ConfigValidationResult(is_valid=True)
        
        # Check required keys
        for key, spec in self._schema.get('required', {}).items():
            if key not in config:
                result.missing_keys.append(key)
                result.errors.append(f"Missing required configuration key: {key}")
                result.is_valid = False
            else:
                # Validate type
                expected_type = spec['type']
                if not isinstance(config[key], expected_type):
                    result.invalid_values.append(key)
                    result.errors.append(f"Invalid type for {key}: expected {expected_type.__name__}, got {type(config[key]).__name__}")
                    result.is_valid = False
        
        # Check optional keys
        for key, spec in self._schema.get('optional', {}).items():
            if key in config:
                value = config[key]
                expected_type = spec['type']
                
                # Type validation
                if not isinstance(value, expected_type):
                    result.invalid_values.append(key)
                    result.errors.append(f"Invalid type for {key}: expected {expected_type.__name__}, got {type(value).__name__}")
                    result.is_valid = False
                    continue
                
                # Range validation for integers
                if expected_type == int:
                    if 'min' in spec and value < spec['min']:
                        result.invalid_values.append(key)
                        result.errors.append(f"Value for {key} ({value}) is below minimum ({spec['min']})")
                        result.is_valid = False
                    if 'max' in spec and value > spec['max']:
                        result.invalid_values.append(key)
                        result.errors.append(f"Value for {key} ({value}) is above maximum ({spec['max']})")
                        result.is_valid = False
                
                # Choice validation for strings
                if expected_type == str and 'choices' in spec:
                    if value not in spec['choices']:
                        result.invalid_values.append(key)
                        result.errors.append(f"Invalid choice for {key}: {value} not in {spec['choices']}")
                        result.is_valid = False
        
        # Custom validations
        self._validate_paths(config, result)
        self._validate_ports(config, result)
        
        return result
    
    def _validate_paths(self, config: Dict[str, Any], result: ConfigValidationResult) -> None:
        """Validate path-related configuration."""
        # Check recon_base_dir exists
        if 'recon_base_dir' in config:
            recon_dir = Path(config['recon_base_dir'])
            if not recon_dir.exists():
                result.warnings.append(f"Recon base directory does not exist: {recon_dir}")
            elif not recon_dir.is_dir():
                result.errors.append(f"Recon base directory is not a directory: {recon_dir}")
                result.is_valid = False
        
        # Check tool paths
        for tool in ['naabu_path', 'httpx_path', 'nmap_path']:
            if tool in config and config[tool]:
                # Only validate if not using default system PATH
                if '/' in config[tool] or '\\' in config[tool]:
                    tool_path = Path(config[tool])
                    if not tool_path.exists():
                        result.warnings.append(f"Tool binary not found: {config[tool]}")
    
    def _validate_ports(self, config: Dict[str, Any], result: ConfigValidationResult) -> None:
        """Validate port-related configuration."""
        for port_key in ['custom_ports', 'exclude_ports', 'common_https_ports']:
            if port_key in config and isinstance(config[port_key], list):
                for port in config[port_key]:
                    if not isinstance(port, int) or not (1 <= port <= 65535):
                        result.errors.append(f"Invalid port in {port_key}: {port}")
                        result.is_valid = False
    
    def get_config(self) -> Dict[str, Any]:
        """Get the current configuration."""
        return self._config.copy()
    
    def get_schema(self) -> Dict[str, Any]:
        """Get the configuration schema."""
        return self._schema.copy()
    
    def save_config(self, config: Dict[str, Any], output_file: Path) -> None:
        """
        Save configuration to file.
        
        Args:
            config: Configuration to save
            output_file: Output file path
        """
        try:
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            if output_file.suffix.lower() in ['.yaml', '.yml']:
                with open(output_file, 'w', encoding='utf-8') as f:
                    yaml.dump(config, f, default_flow_style=False, indent=2)
            elif output_file.suffix.lower() == '.json':
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2)
            else:
                raise ConfigurationError(f"Unsupported output format: {output_file.suffix}")
            
            logger.info(f"Configuration saved to {output_file}")
            
        except Exception as e:
            error_msg = f"Failed to save configuration to {output_file}: {e}"
            handle_error(ConfigurationError(error_msg))
            raise

# Global config manager instance
_config_manager = ConfigManager()

def get_config_manager() -> ConfigManager:
    """Get the global configuration manager instance."""
    return _config_manager
