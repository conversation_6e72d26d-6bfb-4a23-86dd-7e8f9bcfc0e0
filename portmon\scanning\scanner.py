# File: portmon/scanner.py
# Description: Refactored to use the centralized command_runner utility.

import time
import logging
import re
import hashlib
from pathlib import Path
from typing import Set, Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timezone
from colorama import Fore, Style, init as colorama_init

# Import the centralized command utility
from .command_runner import run_command, parse_line_based_output

colorama_init()
logger = logging.getLogger(__name__)

@dataclass
class ScanMetrics:
    """Track scanning performance metrics."""
    total_scans: int = 0
    successful_scans: int = 0
    failed_scans: int = 0
    total_ports_found: int = 0
    total_scan_time: float = 0.0
    last_scan_time: Optional[datetime] = None

    def record_scan(self, success: bool, ports_found: int, scan_time: float):
        """Record a scan result."""
        self.total_scans += 1
        self.total_scan_time += scan_time
        self.last_scan_time = datetime.now(timezone.utc)

        if success:
            self.successful_scans += 1
            self.total_ports_found += ports_found
        else:
            self.failed_scans += 1

    def get_success_rate(self) -> float:
        """Get scan success rate as percentage."""
        return (self.successful_scans / self.total_scans * 100) if self.total_scans > 0 else 0.0

    def get_average_scan_time(self) -> float:
        """Get average scan time in seconds."""
        return (self.total_scan_time / self.total_scans) if self.total_scans > 0 else 0.0

    def get_average_ports_per_scan(self) -> float:
        """Get average ports found per successful scan."""
        return (self.total_ports_found / self.successful_scans) if self.successful_scans > 0 else 0.0

# Global metrics instance
_scan_metrics = ScanMetrics()

def validate_target(target: str) -> bool:
    """
    Validate target format (domain or IP).

    Args:
        target: Target string to validate

    Returns:
        True if target format is valid
    """
    if not target or not isinstance(target, str):
        return False

    # Basic domain/IP validation
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

    return bool(re.match(domain_pattern, target) or re.match(ip_pattern, target))

def validate_ports(ports: List[int]) -> bool:
    """
    Validate port numbers.

    Args:
        ports: List of port numbers to validate

    Returns:
        True if all ports are valid
    """
    if not isinstance(ports, list):
        return False

    return all(isinstance(port, int) and 1 <= port <= 65535 for port in ports)

def get_scan_cache_key(target: str, scan_mode: str, ports: List[int]) -> str:
    """
    Generate a cache key for scan results.

    Args:
        target: Target being scanned
        scan_mode: Scan mode used
        ports: Ports being scanned

    Returns:
        Cache key string
    """
    ports_str = ','.join(map(str, sorted(ports)))
    cache_input = f"{target}:{scan_mode}:{ports_str}"
    return hashlib.md5(cache_input.encode()).hexdigest()[:16]

def get_scan_metrics() -> ScanMetrics:
    """Get global scan metrics."""
    return _scan_metrics

def run_naabu_scan(config: Dict[str, Any],
                   paths: Dict[str, Path],
                   target: str,
                   scan_mode: str = 'regular') -> Optional[Set[str]]:
    """
    Enhanced Naabu port scan with validation, metrics, and error recovery.

    Args:
        config: The global configuration dictionary.
        paths: Dict with keys 'subdomains_file' and 'scan_output_dir'.
        target: The target identifier (e.g., 'example.com').
        scan_mode: Scan mode determining port settings.

    Returns:
        A set of "host:port" strings discovered, or None on critical failure.
    """
    scan_start_time = time.time()
    discovered_ports: Set[str] = set()

    # Input validation
    if not validate_target(target):
        logger.error(f"{Fore.RED}[-] Invalid target format: {target}{Style.RESET_ALL}")
        _scan_metrics.record_scan(False, 0, time.time() - scan_start_time)
        return None

    # Validate paths
    if not isinstance(paths, dict) or 'subdomains_file' not in paths or 'scan_output_dir' not in paths:
        logger.error(f"{Fore.RED}[-] Invalid paths configuration for target {target}{Style.RESET_ALL}")
        _scan_metrics.record_scan(False, 0, time.time() - scan_start_time)
        return None

    timestamp = int(time.time())
    safe_name = "".join(c if c.isalnum() else "_" for c in target)
    subdomains_file = paths['subdomains_file']
    output_file = paths['scan_output_dir'] / f"naabu_{safe_name}_{scan_mode}_{timestamp}.txt"
    naabu_path = config.get('naabu_path', 'naabu')

    # Validate subdomains file
    if not subdomains_file.is_file() or subdomains_file.stat().st_size == 0:
        logger.error(f"{Fore.RED}[-] Subdomains file missing/empty: {subdomains_file}. Cannot scan {target}.{Style.RESET_ALL}")
        _scan_metrics.record_scan(False, 0, time.time() - scan_start_time)
        return None

    # Base command + Global Concurrency Flag
    naabu_concurrency = str(config.get('naabu_concurrency', 100))
    cmd = [
        naabu_path,
        '-l', str(subdomains_file),
        '-o', str(output_file),
        '-c', naabu_concurrency
    ]
    
    # Add custom naabu flags from config
    naabu_flags = config.get('naabu_flags', ['-silent'])
    if naabu_flags:
        # Add each flag individually
        for flag in naabu_flags:
            # Skip any already added flags to avoid duplicates
            if flag not in ['-l', '-o', '-c', '-rate', '-p', '-top-ports', '-exclude-ports']:
                cmd.append(flag)
        logger.debug(f"Using custom naabu flags: {naabu_flags}")
    else:
        # Fallback to silent flag if none specified
        cmd.append('-silent')

    # --- ADDED: Naabu Rate Limiting ---
    naabu_rate = config.get('naabu_rate', 0)
    if naabu_rate > 0:
        cmd.extend(['-rate', str(naabu_rate)])
        logger.info(f"Naabu [{scan_mode}] using rate limit: {naabu_rate} pps for {target}")
    # ---------------------------------

    # Port settings based on optimized scan mode
    exclude_ports = config.get('exclude_ports', [])
    if exclude_ports:
        excl = ",".join(map(str, exclude_ports))  # Convert integers to strings
        cmd.extend(['-exclude-ports', excl])

    scan_type_log = f"{scan_mode.replace('_', ' ').title()}"
    port_setting_log = ""

    # Optimized port selection logic
    if scan_mode == 'daily_top_1000':
        # Force top 1000 for daily scans
        top_ports_count = config.get('top_ports', 1000)
        cmd.extend(['-top-ports', str(top_ports_count)])
        port_setting_log = f"using top {top_ports_count} ports (daily scan)"
    elif scan_mode == 'combined':
        # Smart combination of top ports + custom ports (deduplicated)
        custom_ports = config.get('custom_ports', [])
        top_ports_config = config.get('top_ports', 1000)

        if custom_ports and top_ports_config > 0:
            # Use top ports but add custom ports that aren't in typical top ranges
            cmd.extend(['-top-ports', str(top_ports_config)])
            # Add custom ports that are likely not in top N
            additional_ports = [p for p in custom_ports if p > 10000 or p in [25, 587, 465, 993, 995]]
            if additional_ports:
                pts = ",".join(map(str, additional_ports))
                cmd.extend(['-p', pts])
                port_setting_log = f"using top {top_ports_config} + {len(additional_ports)} additional custom ports"
            else:
                port_setting_log = f"using top {top_ports_config} ports (covers all custom ports)"
        elif custom_ports:
            pts = ",".join(map(str, custom_ports))
            cmd.extend(['-p', pts])
            port_setting_log = f"using {len(custom_ports)} custom ports only"
        else:
            cmd.extend(['-top-ports', str(top_ports_config)])
            port_setting_log = f"using top {top_ports_config} ports"
    elif scan_mode == 'custom_ports':
        # Custom ports only
        custom_ports = config.get('custom_ports', [])
        if custom_ports:
            pts = ",".join(map(str, custom_ports))
            cmd.extend(['-p', pts])
            port_setting_log = f"using {len(custom_ports)} custom ports"
        else:
            logger.warning(f"Custom ports scan requested but no custom_ports configured for {target}")
            return set()
    elif scan_mode == 'top_ports':
        # Top ports only
        top_ports_config = config.get('top_ports', 1000)
        cmd.extend(['-top-ports', str(top_ports_config)])
        port_setting_log = f"using top {top_ports_config} ports"
    else:
        # Legacy fallback for 'regular' mode
        custom_ports = config.get('custom_ports', [])
        top_ports_config = config.get('top_ports', 1000)

        if custom_ports:
            pts = ",".join(map(str, custom_ports))
            cmd.extend(['-p', pts])
            port_setting_log = f"using {len(custom_ports)} custom ports"
        elif top_ports_config > 0:
            cmd.extend(['-top-ports', str(top_ports_config)])
            port_setting_log = f"using top {top_ports_config} ports"
        else:
            port_setting_log = "using Naabu default ports"

    # Log combined info
    logger.info(
        f"{Fore.YELLOW}[*] Running Naabu {scan_type_log} scan for {target} "
        f"({port_setting_log}"
        f"{f', excluding {len(exclude_ports)}' if exclude_ports else ''}"
        f"{f', rate {naabu_rate} pps' if naabu_rate > 0 else ''}"
        f"), output → {output_file.name}{Style.RESET_ALL}"
    )
    logger.debug(f"Naabu command: {' '.join(cmd)}")

    # Get timeout from config
    process_timeout = config.get('naabu_timeout_seconds', 5400)
    
    # Use the centralized command runner
    success, _, _, _ = run_command(
        cmd=cmd,
        timeout=process_timeout,
        description=f"Naabu [{scan_mode}] scan for {target}",
        output_file=output_file,
        max_output_length=1000,
        stream_output=False  # Set to True for very large scans
    )
    
    if not success:
        logger.warning(f"{Fore.YELLOW}[!] Naabu scan may have encountered issues for {target}{Style.RESET_ALL}")
        # Continue to check for output file anyway, as it might have partial results

    # Parse output using the centralized parser
    if output_file.exists():
        try:
            # Parse with centralized utility
            lines = parse_line_based_output(output_file=output_file)
            discovered_ports = {line for line in lines if ':' in line}

            # Validate discovered ports
            valid_ports = set()
            for port_entry in discovered_ports:
                if ':' in port_entry:
                    try:
                        _, port_str = port_entry.rsplit(':', 1)
                        port_num = int(port_str)
                        if 1 <= port_num <= 65535:
                            valid_ports.add(port_entry)
                        else:
                            logger.warning(f"Invalid port number in result: {port_entry}")
                    except ValueError:
                        logger.warning(f"Invalid port format in result: {port_entry}")

            discovered_ports = valid_ports
            scan_time = time.time() - scan_start_time

            logger.info(f"{Fore.GREEN}[+] Found {len(discovered_ports)} valid ports from {scan_type_log} scan for {target} in {scan_time:.2f}s.{Style.RESET_ALL}")

            # Record successful scan metrics
            _scan_metrics.record_scan(True, len(discovered_ports), scan_time)

        except Exception as e:
            scan_time = time.time() - scan_start_time
            logger.error(f"{Fore.RED}[-] Error processing Naabu output {output_file}: {e}{Style.RESET_ALL}", exc_info=True)
            _scan_metrics.record_scan(False, 0, scan_time)
            return set()  # Return empty set on parse error
    else:
        scan_time = time.time() - scan_start_time
        logger.warning(f"{Fore.YELLOW}[!] Naabu output file missing for {target} ({scan_type_log} scan){Style.RESET_ALL}")
        _scan_metrics.record_scan(False, 0, scan_time)
        return set()  # Return empty set if file is missing

    return discovered_ports