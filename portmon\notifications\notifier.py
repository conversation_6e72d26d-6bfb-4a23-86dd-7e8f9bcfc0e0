# File: portmon/notifier.py
# Description: Added Nmap results to notifications, critical flag handling,
# persistent HTTP sessions, and strongly-typed results.

import requests
import logging
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from dataclasses import dataclass
from colorama import Fore, Style, init as colorama_init

# Import shared HTTP session utils
from .http_utils import send_post, wait_for_rate_limit, close_session

colorama_init()
logger = logging.getLogger(__name__)

# Max characters for Discord embed field values and total description
MAX_FIELD_VALUE_LEN = 1020
MAX_DESCRIPTION_LEN = 4090 # Slightly less than 4096 limit
MAX_EMBED_TOTAL = 6000 # Discord total embed size limit

# Dataclasses for strongly-typed results
@dataclass
class HttpProbeResult:
    url: Optional[str] = None
    status_code: Optional[int] = None
    title: Optional[str] = None
    tech: List[str] = None
    # Removed unused fields (headers, body_preview)
    
    def __post_init__(self):
        if self.tech is None:
            self.tech = []

@dataclass
class NmapScriptOutput:
    banner: Optional[str] = None
    # Removed unused fields (ssl_info)

@dataclass
class NmapResult:
    service_name: Optional[str] = None
    product: Optional[str] = None
    version: Optional[str] = None
    script_outputs: Optional[NmapScriptOutput] = None
    
    def __post_init__(self):
        if self.script_outputs is None:
            self.script_outputs = NmapScriptOutput()

def _send_discord_request(webhook_url: str, payload: Dict[str, Any], config: Dict[str, Any]) -> bool:
    """Internal function to send payload to Discord with retries."""
    # Apply rate limiting using shared utility
    wait_for_rate_limit()

    headers = {"Content-Type": "application/json"}
    retry_attempts = config.get('discord_retry_attempts', 3)
    retry_delay = config.get('discord_retry_delay', 5)
    timeout = 20 # Request timeout

    for attempt in range(retry_attempts):
        try:
            # Use the shared send_post from http_utils
            response = send_post(webhook_url, json_data=payload, headers=headers, timeout=timeout, rate_limit=True)
            response.raise_for_status() # Check for 4xx/5xx errors
            logger.info(f"{Fore.GREEN}[+] Discord notification sent successfully (Attempt {attempt + 1}). {Fore.GREEN}🔔{Style.RESET_ALL}")
            return True
        except requests.exceptions.Timeout:
            logger.warning(f"{Fore.YELLOW}[!] Discord notification timed out (Attempt {attempt + 1}/{retry_attempts}).{Style.RESET_ALL}")
        except requests.exceptions.HTTPError as e:
            logger.warning(f"{Fore.YELLOW}[!] Discord HTTP error {e.response.status_code} (Attempt {attempt + 1}/{retry_attempts}): {e.response.reason}{Style.RESET_ALL}")
            try: logger.debug(f"Discord error response: {e.response.text}")
            except: pass
            
            # Honor Discord's Retry-After header for rate limiting
            if e.response.status_code == 429: # Rate limited
                retry_after = e.response.headers.get('Retry-After')
                wait_time = float(retry_after) if retry_after else retry_delay * 3
                logger.warning(f"{Fore.YELLOW}[!] Discord rate limit hit! Waiting {wait_time}s as specified by Discord...{Style.RESET_ALL}")
                time.sleep(wait_time)
                continue # Try again immediately after waiting
                
        except requests.exceptions.RequestException as e:
            logger.warning(f"{Fore.YELLOW}[!] Discord notification error (Attempt {attempt + 1}/{retry_attempts}): {e}{Style.RESET_ALL}")

        if attempt < retry_attempts - 1:
            logger.info(f"Retrying Discord notification in {retry_delay} seconds...")
            time.sleep(retry_delay)
        else:
            logger.error(f"{Fore.RED}[-] Max retries reached for Discord notification. Giving up.{Style.RESET_ALL}")
            return False
    return False

def _format_nmap_results(nmap_results: Union[Dict[str, Any], NmapResult]) -> str:
    """Helper to format Nmap results into a string for Discord.
    
    Args:
        nmap_results: Either a NmapResult object or a dictionary with nmap scan results
        
    Returns:
        Formatted string for Discord embed
    """
    parts = []
    
    # Handle both dictionary and NmapResult object
    if isinstance(nmap_results, NmapResult):
        # Handle NmapResult object
        if nmap_results.service_name:
            parts.append(f"**Service:** `{nmap_results.service_name}`")
        if nmap_results.product:
            prod = nmap_results.product
            if nmap_results.version:
                prod += f" `{nmap_results.version}`"
            parts.append(f"**Product:** {prod}")
        elif nmap_results.version:  # Show version even if product is missing
            parts.append(f"**Version:** `{nmap_results.version}`")

        # Handle script outputs
        if nmap_results.script_outputs and nmap_results.script_outputs.banner:
            parts.append(f"**Banner:** `{nmap_results.script_outputs.banner}`")
            
    else:
        # Backward compatibility for dictionary format
        if nmap_results.get('service_name'):
            parts.append(f"**Service:** `{nmap_results['service_name']}`")
        if nmap_results.get('product'):
            prod = nmap_results['product']
            if nmap_results.get('version'):
                prod += f" `{nmap_results['version']}`"
            parts.append(f"**Product:** {prod}")
        elif nmap_results.get('version'):  # Show version even if product is missing
            parts.append(f"**Version:** `{nmap_results['version']}`")

        scripts = nmap_results.get('script_outputs', {})
        if scripts.get('banner'):
            parts.append(f"**Banner:** `{scripts['banner']}`")

    if not parts:
        return "_Nmap scan ran, but no specific service info extracted._"

    text = "\n".join(parts)
    # Truncate if necessary (though unlikely with current formatting)
    if len(text) > MAX_FIELD_VALUE_LEN:
        text = text[:MAX_FIELD_VALUE_LEN - 3] + "..."
    return text


def _build_immediate_embed(target: str, host_port: str, 
                     probe_results: Optional[Union[Dict[str, Any], HttpProbeResult]],
                     nmap_results: Optional[Union[Dict[str, Any], NmapResult]], 
                     is_critical: bool = False, 
                     nmap_enabled: bool = False) -> Dict[str, Any]:
    """
    Build an embed for immediate notification of a newly discovered port.
    
    Args:
        target: The target domain or IP
        host_port: The host:port string
        probe_results: Optional HTTP probe results
        nmap_results: Optional nmap results
        is_critical: Whether this is a critical port
        nmap_enabled: Whether nmap scans are enabled in config
    
    Returns:
        A Discord embed dictionary
    """
    # Construct Embed for single port
    title = "🚨 New Port Discovery!"
    color = 16729344 # Orange (default)
    if is_critical:
        title = "🔥 CRITICAL Port Discovery! 🔥"
        color = 15158332 # Red
    
    # Use ISO-8601 UTC timestamp for consistency
    timestamp = datetime.now(timezone.utc).isoformat()

    embed = {
        "title": title,
        "color": color,
        "fields": [
            {"name": "🎯 Target", "value": f"`{target}`", "inline": True},
            {"name": "📍 Host:Port", "value": f"`{host_port}`", "inline": True}
        ],
        "footer": {"text": f"Portmon Immediate | {timestamp}"},
        "timestamp": timestamp
    }

    # Format HTTPX probe details
    probe_details_text = "_No web service detected or HTTP probe failed._"
    if probe_results:
        if isinstance(probe_results, HttpProbeResult):
            url = probe_results.url or ''
            status = probe_results.status_code
            title_text = (probe_results.title or '').strip()
            tech = probe_results.tech or []
        else:
            url = probe_results.get('url', '')
            status = probe_results.get('status-code')
            title_text = probe_results.get('title', '').strip()
            tech = probe_results.get('tech', [])
            
        parts = []
        if url: parts.append(f"🔗 **URL:** `{url}`")
        if status: parts.append(f"🚦 **Status:** `{status}`")
        if title_text: parts.append(f"📄 **Title:** `{title_text[:150]}`") # Truncate title
        if tech:
            tech_str = ", ".join(f"`{t}`" for t in tech[:10]) # Limit tech items
            if len(tech) > 10: tech_str += f", `+{len(tech) - 10}...`"
            parts.append(f"🛠️ **Tech:** {tech_str}")
        if parts: probe_details_text = "\n".join(parts)
        else: probe_details_text = "_HTTP probe ran, no specific details found._"

    # Add HTTP probe details field, handling length limits
    if len(probe_details_text) > MAX_FIELD_VALUE_LEN:
         probe_details_text = probe_details_text[:MAX_FIELD_VALUE_LEN-3] + "..."

    embed["fields"].append({
        "name": "🔍 HTTP Probe",
        "value": probe_details_text,
        "inline": False
    })

    # --- ADD Nmap Field ---
    if nmap_results:
        nmap_field_value = _format_nmap_results(nmap_results)
        embed["fields"].append({
            "name": "🕵️ Nmap Service Scan",
            "value": nmap_field_value,
            "inline": False
        })
    elif nmap_enabled: # Indicate if scan was enabled but failed/found nothing
         embed["fields"].append({
            "name": "🕵️ Nmap Service Scan",
            "value": "_Scan enabled, but no results obtained._",
            "inline": False
        })
         
    return embed

def _build_digest_embed(target: str, scan_mode: str, added_details: List[Dict[str, Any]], removed_ports: List[str]) -> Dict[str, Any]:
    """
    Build an embed for digest notifications with multiple port changes.
    
    Args:
        target: The target domain or IP
        scan_mode: Type of scan that detected changes
        added_details: List of dictionaries with details of added ports
        removed_ports: List of ports that were removed
    
    Returns:
        A Discord embed dictionary
    """
    scan_type_nice = scan_mode.replace('_', ' ').title()
    num_added = len(added_details)
    num_removed = len(removed_ports)

    # Determine color based on changes
    color = 10181046 # Default Grey
    if num_added > 0 and num_removed == 0:
        color = 3066993 # Green
    elif num_removed > 0 and num_added == 0:
        color = 15158332 # Red
    elif num_added > 0 and num_removed > 0:
        color = 15844367 # Orange
        
    # Use ISO-8601 UTC timestamp for consistency
    timestamp = datetime.now(timezone.utc).isoformat()

    # Base Embed
    embed = {
        "title": f"📊 Portmon Scan Digest: `{target}`",
        "description": f"Non-critical changes detected during **{scan_type_nice}** scan.",
        "color": color,
        "fields": [],
        "footer": {"text": f"Portmon Digest | {timestamp}"},
        "timestamp": timestamp
    }

    # --- Format Added Ports Field ---
    added_field_value = ""
    if num_added > 0:
        added_lines = []
        chars_used = 0
        ports_shown = 0
        max_ports_to_detail = 25 # Limit how many ports we show details for

        for item in added_details[:max_ports_to_detail]:
            host_port = item['host_port']
            probe = item.get('probe_results') # Use .get safely
            nmap = item.get('nmap_results')   # Use .get safely

            line_parts = [f"➕ `{host_port}`"]
            details = []

            # Add HTTP Probe details (concise)
            if probe:
                if isinstance(probe, HttpProbeResult):
                    status = probe.status_code
                    title = (probe.title or '').strip()[:50]
                else:
                    status = probe.get('status-code')
                    title = probe.get('title', '').strip()[:50] # Shorter title
                if status: details.append(f"HTTP:`{status}`")
                if title: details.append(f"Title:`{title}...`")

            # Add Nmap details (concise)
            if nmap:
                if isinstance(nmap, NmapResult):
                    service = nmap.service_name
                    product = nmap.product
                    version = nmap.version
                    banner = nmap.script_outputs.banner if nmap.script_outputs else None
                else:
                    service = nmap.get('service_name')
                    product = nmap.get('product')
                    version = nmap.get('version')
                    banner = nmap.get('script_outputs', {}).get('banner')
                if service: details.append(f"Svc:`{service}`")
                if product: details.append(f"Prod:`{product[:30]}`") # Limit product name len
                if version: details.append(f"Ver:`{version[:20]}`") # Limit version len
                if banner: details.append(f"Banner:`{banner[:40]}...`") # Limit banner len

            if details:
                line_parts.append(f"({' | '.join(details)})") # Join details concisely

            line = " ".join(line_parts) + "\n"

            # Check if adding this line exceeds limits
            if chars_used + len(line) > MAX_FIELD_VALUE_LEN:
                break # Stop adding more details

            added_lines.append(line)
            chars_used += len(line)
            ports_shown += 1

        added_field_value = "".join(added_lines)
        if num_added > ports_shown:
             footer_line = f"\n... and {num_added - ports_shown} more new non-critical ports."
             if chars_used + len(footer_line) <= MAX_FIELD_VALUE_LEN:
                 added_field_value += footer_line
             else:
                 logger.warning(f"Digest notification for {target} truncated added ports list heavily.")

        embed["fields"].append({
            "name": f"🚀 New Non-Critical Ports ({num_added}){' [Truncated]' if num_added > ports_shown else ''}",
            "value": added_field_value if added_field_value else "_None_",
            "inline": False
        })

    # --- Format Removed Ports Field ---
    if num_removed > 0:
        removed_field_value = ""
        chars_used = 0
        ports_shown = 0
        max_ports_to_list = 50 # Max removed ports to list explicitly

        for port in removed_ports[:max_ports_to_list]:
             line = f"`{port}`, "
             if chars_used + len(line) > MAX_FIELD_VALUE_LEN - 20: # Leave space for trailer
                 break
             removed_field_value += line
             chars_used += len(line)
             ports_shown += 1

        # Clean up trailing comma and space
        if removed_field_value.endswith(", "):
             removed_field_value = removed_field_value[:-2]

        if num_removed > ports_shown:
            removed_field_value += f"\n... and {num_removed - ports_shown} more closed ports."

        embed["fields"].append({
            "name": f"💨 Closed Ports ({num_removed}){' [Truncated]' if num_removed > ports_shown else ''}",
            "value": removed_field_value if removed_field_value else "_None_",
            "inline": False
        })
        
    return embed

def send_discord_notification(config: Dict[str, Any],
                              target: str,
                              host_port: str,
                              probe_results: Optional[Union[Dict[str, Any], HttpProbeResult]],
                              nmap_results: Optional[Union[Dict[str, Any], NmapResult]] = None,
                              is_critical: bool = False) -> bool:
    """
    (IMMEDIATE MODE) Sends a notification for a SINGLE newly discovered port.
    Handles critical port highlighting.
    
    Args:
        config: Configuration dictionary with Discord webhook URL and other settings
        target: Target domain or IP being scanned
        host_port: Host:port string of the discovered port
        probe_results: HTTP probe results from httpx (dictionary or HttpProbeResult)
        nmap_results: Nmap service scan results (dictionary or NmapResult)
        is_critical: Whether this is a critical port discovery
        
    Returns:
        bool: True if notification was sent successfully, False otherwise
    """
    webhook_url = config.get('discord_webhook_url')
    if not webhook_url or 'discord.com/api/webhooks/' not in webhook_url:
        logger.debug(f"Immediate notification skipped for {host_port}: Webhook URL invalid/missing.")
        return False
    
    # Use helper function to build the embed
    embed = _build_immediate_embed(
        target=target,
        host_port=host_port,
        probe_results=probe_results,
        nmap_results=nmap_results,
        is_critical=is_critical,
        nmap_enabled=config.get('nmap_scan_enabled', False)
    )
    
    # More accurate embed size calculation
    import json
    payload_json = json.dumps({"embeds": [embed]})
    payload_size = len(payload_json.encode('utf-8'))
    
    if payload_size > MAX_EMBED_TOTAL:
        logger.warning(f"Immediate notification for {host_port} exceeds Discord size limits ({payload_size} bytes). Truncating fields.")
        # Iteratively shorten fields until it fits
        max_field_length = 300  # Start with this length
        while payload_size > MAX_EMBED_TOTAL and max_field_length > 50:
            max_field_length -= 50  # Reduce by 50 chars each iteration
            for field in embed.get("fields", []):
                if len(field["value"]) > max_field_length:
                    field["value"] = field["value"][:max_field_length] + "..."
            payload_json = json.dumps({"embeds": [embed]})
            payload_size = len(payload_json.encode('utf-8'))
    
    payload = {"embeds": [embed]}
    return _send_discord_request(webhook_url, payload, config)


def send_digest_notification(config: Dict[str, Any],
                             target: str,
                             scan_mode: str,
                             added_details: List[Dict[str, Any]], # Now contains probe AND nmap results
                             removed_ports: List[str]) -> bool:
    """
    (DIGEST MODE) Sends a single summary notification for all non-critical changes on a target.
    Includes Nmap results in the added ports section.
    
    Args:
        config: Configuration dictionary with Discord webhook URL and other settings
        target: Target domain or IP being scanned
        scan_mode: Type of scan that detected the changes (regular, daily_top_1000, etc.)
        added_details: List of dictionaries with details about newly discovered ports
        removed_ports: List of ports that were removed/closed
        
    Returns:
        bool: True if notification was sent successfully, False otherwise
    """
    webhook_url = config.get('discord_webhook_url')
    if not webhook_url or 'discord.com/api/webhooks/' not in webhook_url:
        logger.debug(f"Digest notification skipped for {target}: Webhook URL invalid/missing.")
        return False
    
    # Use helper function to build the embed
    embed = _build_digest_embed(
        target=target,
        scan_mode=scan_mode,
        added_details=added_details,
        removed_ports=removed_ports
    )
    
    # Check total embed size more precisely using actual JSON encoding
    import json
    payload_json = json.dumps({"embeds": [embed]})
    payload_size = len(payload_json.encode('utf-8'))
    
    # If embed is too large, either split or truncate
    if payload_size > MAX_EMBED_TOTAL:
        logger.warning(f"Digest notification for {target} exceeds Discord size limits ({payload_size} bytes).")
        
        num_added = len(added_details)
        num_removed = len(removed_ports)
        
        # If we have many ports, split into multiple embeds instead of over-truncating
        if num_added > 20 or num_removed > 30:
            logger.info(f"Splitting large digest for {target} into multiple notifications")
            
            # Send added ports in batches of 15
            if num_added > 0:
                batch_size = 15
                for i in range(0, num_added, batch_size):
                    batch = added_details[i:i+batch_size]
                    batch_embed = _build_digest_embed(
                        target=f"{target} (Batch {i//batch_size + 1}/{(num_added+batch_size-1)//batch_size})",
                        scan_mode=scan_mode,
                        added_details=batch,
                        removed_ports=[]
                    )
                    batch_payload = {"embeds": [batch_embed]}
                    _send_discord_request(config.get('discord_webhook_url'), batch_payload, config)
                    time.sleep(1)  # Brief pause between notifications
            
            # Send removed ports in a separate notification if needed
            if num_removed > 0:
                removed_embed = _build_digest_embed(
                    target=f"{target} (Closed ports)",
                    scan_mode=scan_mode,
                    added_details=[],
                    removed_ports=removed_ports
                )
                removed_payload = {"embeds": [removed_embed]}
                return _send_discord_request(config.get('discord_webhook_url'), removed_payload, config)
            
            return True  # If we got here, we've sent at least one batch successfully
        else:
            # Truncate fields to ensure it fits
            max_field_length = 300  # Start with this length
            while payload_size > MAX_EMBED_TOTAL and max_field_length > 50:
                max_field_length -= 50  # Reduce by 50 chars each iteration
                for field in embed.get("fields", []):
                    if len(field["value"]) > max_field_length:
                        field["value"] = field["value"][:max_field_length] + "... [truncated for size limits]"
                # Recalculate size
                payload_json = json.dumps({"embeds": [embed]})
                payload_size = len(payload_json.encode('utf-8'))
    
    payload = {"embeds": [embed]}
    return _send_discord_request(webhook_url, payload, config)