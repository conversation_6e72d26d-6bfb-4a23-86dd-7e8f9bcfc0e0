# File: portmon/utils/system_monitor.py
# Description: System resource monitoring and optimization utilities

import os
import time
import logging
import threading
import platform
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path

# Try to import psutil for advanced system monitoring
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class SystemMetrics:
    """System resource metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    load_average: Optional[List[float]] = None
    process_count: Optional[int] = None
    network_connections: Optional[int] = None

@dataclass
class ProcessMetrics:
    """Process-specific metrics."""
    pid: int
    name: str
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    status: str
    create_time: datetime
    num_threads: int

class SystemMonitor:
    """
    Monitor system resources and provide optimization recommendations.
    """
    
    def __init__(self, monitoring_interval: int = 60):
        """
        Initialize system monitor.
        
        Args:
            monitoring_interval: Interval between monitoring checks in seconds
        """
        self.monitoring_interval = monitoring_interval
        self._metrics_history: List[SystemMetrics] = []
        self._max_history = 1440  # 24 hours at 1-minute intervals
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
    
    def get_current_metrics(self) -> SystemMetrics:
        """Get current system metrics."""
        timestamp = datetime.now(timezone.utc)
        
        if PSUTIL_AVAILABLE:
            # Use psutil for detailed metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            try:
                load_avg = os.getloadavg() if hasattr(os, 'getloadavg') else None
            except (OSError, AttributeError):
                load_avg = None
            
            process_count = len(psutil.pids())
            network_connections = len(psutil.net_connections())
            
            return SystemMetrics(
                timestamp=timestamp,
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available_mb=memory.available / (1024 * 1024),
                disk_usage_percent=disk.percent,
                disk_free_gb=disk.free / (1024 * 1024 * 1024),
                load_average=load_avg,
                process_count=process_count,
                network_connections=network_connections
            )
        else:
            # Fallback to basic OS metrics
            try:
                # Basic CPU and memory info (limited without psutil)
                if platform.system() == "Linux":
                    # Read from /proc for Linux systems
                    with open('/proc/meminfo', 'r') as f:
                        meminfo = f.read()
                    
                    mem_total = 0
                    mem_available = 0
                    for line in meminfo.split('\n'):
                        if line.startswith('MemTotal:'):
                            mem_total = int(line.split()[1]) * 1024  # Convert KB to bytes
                        elif line.startswith('MemAvailable:'):
                            mem_available = int(line.split()[1]) * 1024
                    
                    memory_percent = ((mem_total - mem_available) / mem_total * 100) if mem_total > 0 else 0
                    memory_available_mb = mem_available / (1024 * 1024)
                else:
                    # Fallback for other systems
                    memory_percent = 0
                    memory_available_mb = 0
                
                # Disk usage
                disk_usage = os.statvfs('/')
                disk_total = disk_usage.f_frsize * disk_usage.f_blocks
                disk_free = disk_usage.f_frsize * disk_usage.f_available
                disk_usage_percent = ((disk_total - disk_free) / disk_total * 100) if disk_total > 0 else 0
                disk_free_gb = disk_free / (1024 * 1024 * 1024)
                
                return SystemMetrics(
                    timestamp=timestamp,
                    cpu_percent=0,  # Cannot get CPU without psutil
                    memory_percent=memory_percent,
                    memory_available_mb=memory_available_mb,
                    disk_usage_percent=disk_usage_percent,
                    disk_free_gb=disk_free_gb
                )
                
            except Exception as e:
                logger.warning(f"Failed to get system metrics: {e}")
                return SystemMetrics(
                    timestamp=timestamp,
                    cpu_percent=0,
                    memory_percent=0,
                    memory_available_mb=0,
                    disk_usage_percent=0,
                    disk_free_gb=0
                )
    
    def get_process_metrics(self, pid: Optional[int] = None) -> Optional[ProcessMetrics]:
        """
        Get metrics for a specific process (defaults to current process).
        
        Args:
            pid: Process ID (defaults to current process)
            
        Returns:
            Process metrics or None if not available
        """
        if not PSUTIL_AVAILABLE:
            logger.warning("Process metrics require psutil library")
            return None
        
        try:
            process = psutil.Process(pid) if pid else psutil.Process()
            
            with process.oneshot():
                return ProcessMetrics(
                    pid=process.pid,
                    name=process.name(),
                    cpu_percent=process.cpu_percent(),
                    memory_percent=process.memory_percent(),
                    memory_mb=process.memory_info().rss / (1024 * 1024),
                    status=process.status(),
                    create_time=datetime.fromtimestamp(process.create_time(), tz=timezone.utc),
                    num_threads=process.num_threads()
                )
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess) as e:
            logger.warning(f"Failed to get process metrics for PID {pid}: {e}")
            return None
    
    def start_monitoring(self) -> None:
        """Start continuous system monitoring."""
        if self._monitoring:
            logger.warning("System monitoring is already running")
            return
        
        self._monitoring = True
        self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self._monitor_thread.start()
        logger.info(f"Started system monitoring with {self.monitoring_interval}s interval")
    
    def stop_monitoring(self) -> None:
        """Stop continuous system monitoring."""
        self._monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Stopped system monitoring")
    
    def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        while self._monitoring:
            try:
                metrics = self.get_current_metrics()
                
                with self._lock:
                    self._metrics_history.append(metrics)
                    
                    # Keep only recent history
                    if len(self._metrics_history) > self._max_history:
                        self._metrics_history = self._metrics_history[-self._max_history:]
                
                # Log warnings for high resource usage
                if metrics.cpu_percent > 90:
                    logger.warning(f"High CPU usage: {metrics.cpu_percent:.1f}%")
                if metrics.memory_percent > 90:
                    logger.warning(f"High memory usage: {metrics.memory_percent:.1f}%")
                if metrics.disk_usage_percent > 90:
                    logger.warning(f"High disk usage: {metrics.disk_usage_percent:.1f}%")
                
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.monitoring_interval)
    
    def get_metrics_history(self, hours: int = 1) -> List[SystemMetrics]:
        """
        Get metrics history for the specified number of hours.
        
        Args:
            hours: Number of hours of history to return
            
        Returns:
            List of system metrics
        """
        cutoff_time = datetime.now(timezone.utc).timestamp() - (hours * 3600)
        
        with self._lock:
            return [
                m for m in self._metrics_history 
                if m.timestamp.timestamp() > cutoff_time
            ]
    
    def get_resource_summary(self, hours: int = 1) -> Dict[str, Any]:
        """
        Get a summary of resource usage over the specified time period.
        
        Args:
            hours: Number of hours to analyze
            
        Returns:
            Resource usage summary
        """
        history = self.get_metrics_history(hours)
        
        if not history:
            return {"error": "No metrics history available"}
        
        cpu_values = [m.cpu_percent for m in history if m.cpu_percent > 0]
        memory_values = [m.memory_percent for m in history]
        disk_values = [m.disk_usage_percent for m in history]
        
        summary = {
            "period_hours": hours,
            "samples": len(history),
            "cpu": {
                "avg": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                "max": max(cpu_values) if cpu_values else 0,
                "min": min(cpu_values) if cpu_values else 0
            },
            "memory": {
                "avg": sum(memory_values) / len(memory_values) if memory_values else 0,
                "max": max(memory_values) if memory_values else 0,
                "min": min(memory_values) if memory_values else 0,
                "current_available_mb": history[-1].memory_available_mb if history else 0
            },
            "disk": {
                "avg": sum(disk_values) / len(disk_values) if disk_values else 0,
                "max": max(disk_values) if disk_values else 0,
                "min": min(disk_values) if disk_values else 0,
                "current_free_gb": history[-1].disk_free_gb if history else 0
            }
        }
        
        return summary
    
    def get_optimization_recommendations(self) -> List[str]:
        """
        Get system optimization recommendations based on current metrics.
        
        Returns:
            List of optimization recommendations
        """
        recommendations = []
        current = self.get_current_metrics()
        
        # CPU recommendations
        if current.cpu_percent > 80:
            recommendations.append("High CPU usage detected. Consider reducing scan concurrency.")
        
        # Memory recommendations
        if current.memory_percent > 85:
            recommendations.append("High memory usage detected. Consider processing targets in smaller batches.")
        elif current.memory_available_mb < 500:
            recommendations.append("Low available memory. Consider enabling memory optimization features.")
        
        # Disk recommendations
        if current.disk_usage_percent > 90:
            recommendations.append("Disk space is running low. Consider cleaning up old scan results.")
        elif current.disk_free_gb < 1:
            recommendations.append("Very low disk space. Enable automatic cleanup of temporary files.")
        
        # Load average recommendations (Linux only)
        if current.load_average and len(current.load_average) > 0:
            if current.load_average[0] > os.cpu_count() * 2:
                recommendations.append("High system load detected. Consider reducing parallel operations.")
        
        if not recommendations:
            recommendations.append("System resources are within normal ranges.")
        
        return recommendations

# Global system monitor instance
_system_monitor = SystemMonitor()

def get_system_monitor() -> SystemMonitor:
    """Get the global system monitor instance."""
    return _system_monitor

def check_system_requirements() -> Dict[str, Any]:
    """
    Check if system meets minimum requirements for portmon.
    
    Returns:
        Dictionary with requirement check results
    """
    results = {
        "meets_requirements": True,
        "checks": {},
        "recommendations": []
    }
    
    current = _system_monitor.get_current_metrics()
    
    # Memory check (minimum 1GB available)
    if current.memory_available_mb < 1024:
        results["meets_requirements"] = False
        results["checks"]["memory"] = {
            "status": "FAIL",
            "current": f"{current.memory_available_mb:.0f}MB",
            "required": "1024MB",
            "message": "Insufficient available memory"
        }
        results["recommendations"].append("Increase available memory or close other applications")
    else:
        results["checks"]["memory"] = {
            "status": "PASS",
            "current": f"{current.memory_available_mb:.0f}MB",
            "required": "1024MB"
        }
    
    # Disk space check (minimum 5GB free)
    if current.disk_free_gb < 5:
        results["meets_requirements"] = False
        results["checks"]["disk"] = {
            "status": "FAIL",
            "current": f"{current.disk_free_gb:.1f}GB",
            "required": "5GB",
            "message": "Insufficient disk space"
        }
        results["recommendations"].append("Free up disk space or use a different output directory")
    else:
        results["checks"]["disk"] = {
            "status": "PASS",
            "current": f"{current.disk_free_gb:.1f}GB",
            "required": "5GB"
        }
    
    # Python version check
    import sys
    python_version = sys.version_info
    if python_version < (3, 8):
        results["meets_requirements"] = False
        results["checks"]["python"] = {
            "status": "FAIL",
            "current": f"{python_version.major}.{python_version.minor}",
            "required": "3.8+",
            "message": "Python version too old"
        }
        results["recommendations"].append("Upgrade to Python 3.8 or newer")
    else:
        results["checks"]["python"] = {
            "status": "PASS",
            "current": f"{python_version.major}.{python_version.minor}",
            "required": "3.8+"
        }
    
    return results
