#!/usr/bin/env python3
# File: portmon/config/validate_config.py
# Description: Configuration validation CLI tool

import sys
import argparse
import logging
from pathlib import Path
from typing import Dict, Any

from .config_manager import (
    validate_config_file, 
    load_config, 
    get_config_summary,
    check_tool_availability,
    cleanup_old_backups
)
from .state_manager import validate_state_file, cleanup_old_state_backups

def setup_logging(verbose: bool = False) -> None:
    """Setup logging for the validation tool."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s'
    )

def validate_tools(config_dict: Dict[str, Any]) -> bool:
    """
    Validate that all required tools are available.
    
    Args:
        config_dict: Configuration dictionary
        
    Returns:
        True if all tools are available, False otherwise
    """
    tools = {
        'naabu': config_dict.get('naabu_path', 'naabu'),
        'httpx': config_dict.get('httpx_path', 'httpx'),
        'nmap': config_dict.get('nmap_path', 'nmap')
    }
    
    all_available = True
    for tool_name, tool_path in tools.items():
        if not check_tool_availability(tool_path, tool_name):
            print(f"❌ Tool {tool_name} not available at: {tool_path}")
            all_available = False
        else:
            print(f"✅ Tool {tool_name} available at: {tool_path}")
    
    return all_available

def validate_config_command(args) -> int:
    """Validate configuration file."""
    config_path = Path(args.config)
    
    if not config_path.exists():
        print(f"❌ Configuration file not found: {config_path}")
        return 1
    
    print(f"🔍 Validating configuration file: {config_path}")
    
    # Validate configuration syntax and schema
    if not validate_config_file(config_path):
        print("❌ Configuration validation failed")
        return 1
    
    print("✅ Configuration file is valid")
    
    # Load and analyze configuration
    try:
        config_dict = load_config()
        if not config_dict:
            print("❌ Failed to load configuration")
            return 1
        
        # Show configuration summary
        summary = get_config_summary(config_dict)
        print("\n📊 Configuration Summary:")
        print(f"  • Targets: {summary['targets_count']}")
        print(f"  • Scan interval: {summary['scan_interval_hours']:.1f} hours")
        print(f"  • Max concurrent targets: {summary['max_concurrent_targets']}")
        print(f"  • Custom ports: {summary['custom_ports_count']}")
        print(f"  • Critical ports: {summary['critical_ports_count']}")
        print(f"  • Nmap enabled: {summary['nmap_enabled']}")
        print(f"  • Notification mode: {summary['notification_mode']}")
        
        # Validate tools if requested
        if args.check_tools:
            print("\n🔧 Checking tool availability:")
            if not validate_tools(config_dict):
                print("\n⚠️  Some tools are not available. Set PORTMON_SKIP_TOOL_VALIDATION=true to skip this check.")
                return 1
        
        print("\n✅ Configuration is valid and ready to use!")
        return 0
        
    except Exception as e:
        print(f"❌ Error analyzing configuration: {e}")
        return 1

def cleanup_command(args) -> int:
    """Clean up old backup files."""
    print("🧹 Cleaning up old backup files...")
    
    try:
        # Clean up config backups
        cleanup_old_backups(args.max_backups)
        
        # Clean up state backups if state directory exists
        if args.state_dir:
            state_dir = Path(args.state_dir)
            if state_dir.exists():
                for state_file in state_dir.glob("*.json"):
                    cleanup_old_state_backups(state_file, args.max_backups)
        
        print("✅ Cleanup completed successfully")
        return 0
        
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        return 1

def validate_state_command(args) -> int:
    """Validate state files."""
    state_path = Path(args.state_file)
    
    if not state_path.exists():
        print(f"❌ State file not found: {state_path}")
        return 1
    
    print(f"🔍 Validating state file: {state_path}")
    
    if validate_state_file(state_path):
        print("✅ State file is valid")
        return 0
    else:
        print("❌ State file validation failed")
        return 1

def main():
    """Main entry point for the validation tool."""
    parser = argparse.ArgumentParser(
        description="Portmon configuration validation and maintenance tool"
    )
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Config validation command
    config_parser = subparsers.add_parser('config', help='Validate configuration file')
    config_parser.add_argument('config', help='Path to configuration file')
    config_parser.add_argument('--check-tools', action='store_true', 
                              help='Also check tool availability')
    
    # State validation command
    state_parser = subparsers.add_parser('state', help='Validate state file')
    state_parser.add_argument('state_file', help='Path to state file')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old backup files')
    cleanup_parser.add_argument('--max-backups', type=int, default=10,
                               help='Maximum number of backup files to keep (default: 10)')
    cleanup_parser.add_argument('--state-dir', help='State directory to clean up')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    setup_logging(args.verbose)
    
    if args.command == 'config':
        return validate_config_command(args)
    elif args.command == 'state':
        return validate_state_command(args)
    elif args.command == 'cleanup':
        return cleanup_command(args)
    else:
        print(f"Unknown command: {args.command}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
