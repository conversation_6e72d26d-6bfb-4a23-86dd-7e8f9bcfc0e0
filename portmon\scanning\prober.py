# File: portmon/prober.py
# Description: Refactored to use centralized command_runner with improved error handling and type safety.

import json
import logging
import time
import re
from json import JSONDecodeError
from typing import Optional, Dict, Any, Set, List
from dataclasses import dataclass
from datetime import datetime, timezone

from .command_runner import run_command
from ..notifications.notifier import HttpProbeResult

logger = logging.getLogger(__name__)

@dataclass
class ProbeMetrics:
    """Track HTTP probing performance metrics."""
    total_probes: int = 0
    successful_probes: int = 0
    failed_probes: int = 0
    total_probe_time: float = 0.0
    last_probe_time: Optional[datetime] = None

    def record_probe(self, success: bool, probe_time: float):
        """Record a probe result."""
        self.total_probes += 1
        self.total_probe_time += probe_time
        self.last_probe_time = datetime.now(timezone.utc)

        if success:
            self.successful_probes += 1
        else:
            self.failed_probes += 1

    def get_success_rate(self) -> float:
        """Get probe success rate as percentage."""
        return (self.successful_probes / self.total_probes * 100) if self.total_probes > 0 else 0.0

    def get_average_probe_time(self) -> float:
        """Get average probe time in seconds."""
        return (self.total_probe_time / self.total_probes) if self.total_probes > 0 else 0.0

# Global metrics instance
_probe_metrics = ProbeMetrics()

def validate_host_port(host_port: str) -> bool:
    """
    Validate host:port format.

    Args:
        host_port: Host:port string to validate

    Returns:
        True if format is valid
    """
    if not host_port or not isinstance(host_port, str):
        return False

    if ':' not in host_port:
        return False

    try:
        host, port_str = host_port.rsplit(':', 1)
        port = int(port_str)

        # Validate host (basic check)
        if not host or len(host) > 253:
            return False

        # Validate port range
        if not (1 <= port <= 65535):
            return False

        return True
    except (ValueError, AttributeError):
        return False

def get_probe_metrics() -> ProbeMetrics:
    """Get global probe metrics."""
    return _probe_metrics

def _parse_first_json_line(output: str) -> Optional[Dict[str, Any]]:
    """
    Helper function to extract the first valid JSON object from a multi-line string.
    
    Args:
        output: String output that may contain JSON objects
        
    Returns:
        The first valid JSON object found, or None if no valid JSON is present
    """
    if not output or not output.strip():
        return None
        
    for line in output.splitlines():
        if not line.strip():
            continue
            
        try:
            return json.loads(line)
        except JSONDecodeError:
            continue
            
    return None


def run_httpx_probe(config: Dict[str, Any], host_port: str) -> Optional[HttpProbeResult]:
    """
    Enhanced httpx probe with validation, metrics, and error recovery.

    Args:
        config: The global configuration dictionary.
        host_port: The host:port string (e.g., 'sub.example.com:8080').

    Returns:
        HttpProbeResult if the probe yielded valid JSON, otherwise None.
    """
    probe_start_time = time.time()

    # Input validation
    if not validate_host_port(host_port):
        logger.warning(f"Invalid host:port format for probe: {host_port}")
        _probe_metrics.record_probe(False, time.time() - probe_start_time)
        return None

    # Use integers for port comparisons as configured in config_manager
    common_https_ports: Set[int] = set(config.get('common_https_ports', [443]))

    try:
        port_str = host_port.split(':')[-1]
        port_int = int(port_str)
        scheme = 'https://' if port_int in common_https_ports else 'http://'
    except (ValueError, IndexError) as e:
        logger.warning(f"Could not determine port from '{host_port}', defaulting scheme to http://: {e}")
        scheme = 'http://'
        _probe_metrics.record_probe(False, time.time() - probe_start_time)
        return None

    url_to_probe = f"{scheme}{host_port}"
    httpx_path = config.get('httpx_path', 'httpx')
    
    # Honor separate timeouts for the probe tool vs process
    probe_timeout = config.get('probe_timeout', 15)  # For httpx -timeout parameter
    process_timeout = config.get('httpx_process_timeout_seconds', probe_timeout + 10)  # For command_runner
    
    threads = str(config.get('httpx_threads', 100))

    cmd = [
        httpx_path,
        '-u', url_to_probe,
        '-silent',
        '-json',
        '-status-code',
        '-title',
        '-tech-detect',
        '-follow-redirects',
        '-timeout', str(probe_timeout),
        '-threads', threads
    ]

    # Use the centralized command_runner instead of direct subprocess.run
    success, stdout, _, _ = run_command(
        cmd=cmd,
        timeout=process_timeout,
        description=f"httpx probe for {url_to_probe}",
        log_level=logging.INFO,
        output_log_level=logging.DEBUG
    )

    # Handle command failure case
    if not success:
        probe_time = time.time() - probe_start_time
        logger.warning(f"Httpx probe failed for {url_to_probe} after {probe_time:.2f}s")
        _probe_metrics.record_probe(False, probe_time)
        # We'll still try to parse stdout if it exists
        if not stdout:
            return None

    # Parse JSON output using the helper
    probe_data = _parse_first_json_line(stdout) if stdout else None
    
    # If we found valid JSON data
    if probe_data:
        status = probe_data.get('status-code', 'N/A')
        title_str = probe_data.get('title', '')
        tech_list = probe_data.get('tech', [])
        
        # Log concise summary
        log_summary = f"Status {status}"
        if title_str: log_summary += f", Title '{title_str[:30]}...'"
        if tech_list: log_summary += f", Tech Detected"
        probe_time = time.time() - probe_start_time
        logger.info(f"Httpx probe successful for {url_to_probe} in {probe_time:.2f}s: {log_summary}")
        _probe_metrics.record_probe(True, probe_time)

        # Return HttpProbeResult dataclass instead of raw dictionary
        return HttpProbeResult(
            url=probe_data.get('url'),
            status_code=probe_data.get('status-code'),
            title=probe_data.get('title'),
            tech=probe_data.get('tech', [])
        )
    else:
        probe_time = time.time() - probe_start_time
        logger.info(f"Httpx probe for {url_to_probe} produced no valid JSON data after {probe_time:.2f}s")
        _probe_metrics.record_probe(False, probe_time)
        return None