# File: portmon/http_utils.py
# Description: Shared HTTP utilities including a global session object

import requests
import logging
import time
from typing import Optional

logger = logging.getLogger(__name__)

# Global shared session for connection pooling
_shared_session: Optional[requests.Session] = None
_last_request_time = 0
_MIN_REQUEST_DELAY = 1.0  # Rate limiting

def get_session() -> requests.Session:
    """
    Get a shared requests Session object for connection pooling.
    Creates a new session if one doesn't exist yet.
    
    Returns:
        A requests.Session object for making HTTP requests
    """
    global _shared_session
    if _shared_session is None:
        logger.debug("Creating new shared HTTP session")
        _shared_session = requests.Session()
    
    return _shared_session

def close_session():
    """
    Close the shared HTTP session if it exists.
    Safe to call even if no session was created.
    """
    global _shared_session
    if _shared_session is not None:
        logger.debug("Closing shared HTTP session")
        try:
            _shared_session.close()
            _shared_session = None
            return True
        except Exception as e:
            logger.error(f"Error closing shared HTTP session: {e}")
            return False
    return True

def wait_for_rate_limit():
    """
    Apply rate limiting for API requests if needed.
    """
    global _last_request_time
    now = time.time()
    wait_time = _MIN_REQUEST_DELAY - (now - _last_request_time)
    
    if wait_time > 0:
        logger.debug(f"Rate limiting HTTP request. Waiting {wait_time:.2f}s.")
        time.sleep(wait_time)
    
    _last_request_time = time.time()

# Convenience function for typical POST requests
def send_post(url: str, json_data: dict, headers: Optional[dict] = None, 
             timeout: int = 30, rate_limit: bool = True) -> requests.Response:
    """
    Send a POST request using the shared session with optional rate limiting.
    
    Args:
        url: The URL to send the request to
        json_data: JSON payload to send
        headers: Optional HTTP headers
        timeout: Request timeout in seconds
        rate_limit: Whether to apply rate limiting
        
    Returns:
        The requests.Response object
    """
    if rate_limit:
        wait_for_rate_limit()
        
    if headers is None:
        headers = {"Content-Type": "application/json"}
    
    session = get_session()
    return session.post(url, json=json_data, headers=headers, timeout=timeout)
