# File: portmon/http_utils.py
# Description: Shared HTTP utilities including a global session object

import requests
import logging
import time
import threading
from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timezone

logger = logging.getLogger(__name__)

@dataclass
class HttpMetrics:
    """Track HTTP request metrics."""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_response_time: float = 0.0
    last_request_time: Optional[datetime] = None

    def record_request(self, success: bool, response_time: float):
        """Record a request and its metrics."""
        self.total_requests += 1
        self.total_response_time += response_time
        self.last_request_time = datetime.now(timezone.utc)

        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1

    def get_average_response_time(self) -> float:
        """Get average response time in seconds."""
        return (self.total_response_time / self.total_requests) if self.total_requests > 0 else 0.0

    def get_success_rate(self) -> float:
        """Get success rate as percentage."""
        return (self.successful_requests / self.total_requests * 100) if self.total_requests > 0 else 0.0

# Global shared session for connection pooling
_shared_session: Optional[requests.Session] = None
_last_request_time = 0
_MIN_REQUEST_DELAY = 1.0  # Rate limiting
_http_metrics = HttpMetrics()
_session_lock = threading.Lock()

def get_session() -> requests.Session:
    """
    Get a shared requests Session object for connection pooling.
    Creates a new session if one doesn't exist yet.
    Thread-safe implementation.

    Returns:
        A requests.Session object for making HTTP requests
    """
    global _shared_session

    with _session_lock:
        if _shared_session is None:
            logger.debug("Creating new shared HTTP session")
            _shared_session = requests.Session()
            # Configure session with reasonable defaults
            _shared_session.headers.update({
                'User-Agent': 'Portmon/1.0 (Port Monitoring Tool)'
            })
            # Set connection pool parameters
            adapter = requests.adapters.HTTPAdapter(
                pool_connections=10,
                pool_maxsize=20,
                max_retries=3
            )
            _shared_session.mount('http://', adapter)
            _shared_session.mount('https://', adapter)

    return _shared_session

def close_session():
    """
    Close the shared HTTP session if it exists.
    Safe to call even if no session was created.
    Thread-safe implementation.
    """
    global _shared_session

    with _session_lock:
        if _shared_session is not None:
            logger.debug("Closing shared HTTP session")
            try:
                _shared_session.close()
                _shared_session = None
                return True
            except Exception as e:
                logger.error(f"Error closing shared HTTP session: {e}")
                return False
    return True

def wait_for_rate_limit():
    """
    Apply rate limiting for API requests if needed.
    """
    global _last_request_time
    now = time.time()
    wait_time = _MIN_REQUEST_DELAY - (now - _last_request_time)
    
    if wait_time > 0:
        logger.debug(f"Rate limiting HTTP request. Waiting {wait_time:.2f}s.")
        time.sleep(wait_time)
    
    _last_request_time = time.time()

# Convenience function for typical POST requests
def send_post(url: str, json_data: dict, headers: Optional[dict] = None,
             timeout: int = 30, rate_limit: bool = True) -> requests.Response:
    """
    Send a POST request using the shared session with optional rate limiting.
    Includes metrics tracking and enhanced error handling.

    Args:
        url: The URL to send the request to
        json_data: JSON payload to send
        headers: Optional HTTP headers
        timeout: Request timeout in seconds
        rate_limit: Whether to apply rate limiting

    Returns:
        The requests.Response object

    Raises:
        requests.RequestException: For HTTP-related errors
    """
    if rate_limit:
        wait_for_rate_limit()

    if headers is None:
        headers = {"Content-Type": "application/json"}

    session = get_session()
    start_time = time.time()

    try:
        response = session.post(url, json=json_data, headers=headers, timeout=timeout)
        response_time = time.time() - start_time
        _http_metrics.record_request(True, response_time)

        logger.debug(f"HTTP POST to {url} completed in {response_time:.2f}s (status: {response.status_code})")
        return response

    except Exception as e:
        response_time = time.time() - start_time
        _http_metrics.record_request(False, response_time)

        logger.error(f"HTTP POST to {url} failed after {response_time:.2f}s: {e}")
        raise

def get_http_metrics() -> HttpMetrics:
    """Get HTTP request metrics."""
    return _http_metrics

def reset_http_metrics() -> None:
    """Reset HTTP metrics (useful for testing)."""
    global _http_metrics
    _http_metrics = HttpMetrics()
