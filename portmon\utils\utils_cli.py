#!/usr/bin/env python3
# File: portmon/utils/utils_cli.py
# Description: Comprehensive utility management and testing CLI tool

import sys
import argparse
import logging
import json
from pathlib import Path
from typing import Dict, Any

from .config_manager import get_config_manager
from .system_monitor import get_system_monitor, check_system_requirements
from .security import get_security_validator
from .data_validator import get_data_validator
from .file_manager import get_file_manager

def setup_logging(verbose: bool = False) -> None:
    """Setup logging for the utility CLI."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(levelname)s: %(message)s'
    )

def show_system_status() -> None:
    """Display comprehensive system status."""
    print("🖥️  System Status Report")
    print("=" * 50)
    
    # System requirements check
    requirements = check_system_requirements()
    print(f"\n✅ System Requirements: {'PASS' if requirements['meets_requirements'] else 'FAIL'}")
    
    for check_name, check_result in requirements['checks'].items():
        status_icon = "✅" if check_result['status'] == 'PASS' else "❌"
        print(f"  • {check_name.title()}: {status_icon} {check_result['current']} (required: {check_result['required']})")
    
    if requirements['recommendations']:
        print(f"\n💡 Recommendations:")
        for rec in requirements['recommendations']:
            print(f"  • {rec}")
    
    # Current system metrics
    monitor = get_system_monitor()
    current_metrics = monitor.get_current_metrics()
    
    print(f"\n📊 Current System Metrics:")
    print(f"  • CPU Usage: {current_metrics.cpu_percent:.1f}%")
    print(f"  • Memory Usage: {current_metrics.memory_percent:.1f}%")
    print(f"  • Available Memory: {current_metrics.memory_available_mb:.0f}MB")
    print(f"  • Disk Usage: {current_metrics.disk_usage_percent:.1f}%")
    print(f"  • Free Disk Space: {current_metrics.disk_free_gb:.1f}GB")
    
    if current_metrics.load_average:
        print(f"  • Load Average: {', '.join(f'{x:.2f}' for x in current_metrics.load_average)}")
    if current_metrics.process_count:
        print(f"  • Process Count: {current_metrics.process_count}")
    
    # Optimization recommendations
    recommendations = monitor.get_optimization_recommendations()
    if len(recommendations) > 1 or "normal ranges" not in recommendations[0]:
        print(f"\n🔧 Optimization Recommendations:")
        for rec in recommendations:
            print(f"  • {rec}")

def validate_configuration(config_file: Path) -> None:
    """Validate a configuration file."""
    print(f"🔍 Validating Configuration: {config_file}")
    print("=" * 50)
    
    try:
        config_manager = get_config_manager()
        config = config_manager.load_config(config_file)
        
        print("✅ Configuration loaded successfully!")
        print(f"\n📋 Configuration Summary:")
        print(f"  • Targets: {len(config.get('targets', []))}")
        print(f"  • Base Directory: {config.get('recon_base_dir', 'Not set')}")
        print(f"  • Naabu Concurrency: {config.get('naabu_concurrency', 'Default')}")
        print(f"  • HTTP Threads: {config.get('httpx_threads', 'Default')}")
        print(f"  • Discord Webhook: {'Configured' if config.get('discord_webhook_url') else 'Not configured'}")
        
        # Validate with data validator
        data_validator = get_data_validator()
        validation_result = data_validator.validate_data(config, 'config')
        
        if validation_result.is_valid:
            print("\n✅ Configuration validation passed!")
        else:
            print("\n❌ Configuration validation failed:")
            for error in validation_result.errors:
                print(f"  • {error}")
        
        if validation_result.warnings:
            print("\n⚠️  Configuration warnings:")
            for warning in validation_result.warnings:
                print(f"  • {warning}")
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")

def test_security_validation() -> None:
    """Test security validation functions."""
    print("🔒 Security Validation Tests")
    print("=" * 40)
    
    validator = get_security_validator()
    
    # Test domain validation
    test_domains = [
        "example.com",
        "sub.example.com", 
        "192.168.1.1",
        "invalid..domain",
        "toolong" + "x" * 250 + ".com",
        ""
    ]
    
    print("\n🌐 Domain Validation:")
    for domain in test_domains:
        is_valid = validator.validate_domain(domain)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        display_domain = domain[:30] + "..." if len(domain) > 30 else domain
        print(f"  • '{display_domain}': {status}")
    
    # Test URL validation
    test_urls = [
        "https://example.com",
        "http://192.168.1.1:8080",
        "ftp://example.com",  # Invalid scheme
        "https://example.com/../../../etc/passwd",  # Path traversal
        ""
    ]
    
    print("\n🔗 URL Validation:")
    for url in test_urls:
        is_valid = validator.validate_url(url)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  • '{url}': {status}")
    
    # Test port validation
    test_ports = [80, 443, 0, 65536, -1, "80", "invalid"]
    
    print("\n🔌 Port Validation:")
    for port in test_ports:
        is_valid = validator.validate_port(port)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  • {port}: {status}")

def cleanup_files(base_dir: Path, max_age_days: int = 7, dry_run: bool = False) -> None:
    """Clean up old files."""
    print(f"🧹 File Cleanup {'(DRY RUN)' if dry_run else ''}")
    print("=" * 40)
    
    file_manager = get_file_manager()
    file_manager.base_dir = base_dir
    
    result = file_manager.cleanup_old_files(max_age_days=max_age_days, dry_run=dry_run)
    
    print(f"\n📊 Cleanup Results:")
    print(f"  • Files removed: {result.files_removed}")
    print(f"  • Directories removed: {result.directories_removed}")
    print(f"  • Space freed: {result.bytes_freed / (1024*1024):.1f}MB")
    
    if result.errors:
        print(f"\n❌ Errors ({len(result.errors)}):")
        for error in result.errors[:5]:  # Show first 5 errors
            print(f"  • {error}")
        if len(result.errors) > 5:
            print(f"  • ... and {len(result.errors) - 5} more errors")

def analyze_directory(directory: Path) -> None:
    """Analyze directory structure and files."""
    print(f"📁 Directory Analysis: {directory}")
    print("=" * 50)
    
    if not directory.exists():
        print("❌ Directory does not exist!")
        return
    
    file_manager = get_file_manager()
    file_manager.base_dir = directory
    
    # Get directory size
    total_size = file_manager.get_directory_size(directory)
    print(f"\n📊 Directory Statistics:")
    print(f"  • Total size: {total_size / (1024*1024):.1f}MB")
    
    # Find large files
    large_files = file_manager.find_files("*", min_size_mb=10)
    if large_files:
        print(f"\n📦 Large Files (>10MB):")
        for file_info in large_files[:10]:  # Show top 10
            size_mb = file_info.size_bytes / (1024*1024)
            print(f"  • {file_info.path.name}: {size_mb:.1f}MB")
    
    # Find old files
    old_files = file_manager.find_files("*", max_age_days=30)
    if old_files:
        print(f"\n🕰️  Old Files (>30 days): {len(old_files)} files")
        total_old_size = sum(f.size_bytes for f in old_files)
        print(f"  • Total size: {total_old_size / (1024*1024):.1f}MB")

def export_schemas() -> None:
    """Export validation schemas to JSON."""
    print("📋 Exporting Validation Schemas")
    print("=" * 40)
    
    data_validator = get_data_validator()
    schemas = {}
    
    for schema_name in data_validator.list_schemas():
        schema_info = data_validator.get_schema_info(schema_name)
        if schema_info:
            schemas[schema_name] = schema_info
    
    output_file = Path("portmon_schemas.json")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(schemas, f, indent=2, default=str)
    
    print(f"✅ Schemas exported to {output_file}")
    print(f"📊 Exported {len(schemas)} schemas:")
    for schema_name in schemas:
        print(f"  • {schema_name}")

def main():
    """Main entry point for the utility CLI."""
    parser = argparse.ArgumentParser(
        description="Portmon utility management and testing tool"
    )
    parser.add_argument('-v', '--verbose', action='store_true', help='Enable verbose output')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # System status command
    subparsers.add_parser('status', help='Show system status and requirements')
    
    # Configuration validation command
    config_parser = subparsers.add_parser('validate-config', help='Validate configuration file')
    config_parser.add_argument('config_file', type=Path, help='Configuration file to validate')
    
    # Security testing command
    subparsers.add_parser('test-security', help='Test security validation functions')
    
    # File cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old files')
    cleanup_parser.add_argument('directory', type=Path, help='Directory to clean')
    cleanup_parser.add_argument('--max-age', type=int, default=7, help='Maximum age in days')
    cleanup_parser.add_argument('--dry-run', action='store_true', help='Show what would be deleted')
    
    # Directory analysis command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze directory structure')
    analyze_parser.add_argument('directory', type=Path, help='Directory to analyze')
    
    # Schema export command
    subparsers.add_parser('export-schemas', help='Export validation schemas to JSON')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    setup_logging(args.verbose)
    
    try:
        if args.command == 'status':
            show_system_status()
            return 0
            
        elif args.command == 'validate-config':
            validate_configuration(args.config_file)
            return 0
            
        elif args.command == 'test-security':
            test_security_validation()
            return 0
            
        elif args.command == 'cleanup':
            cleanup_files(args.directory, args.max_age, args.dry_run)
            return 0
            
        elif args.command == 'analyze':
            analyze_directory(args.directory)
            return 0
            
        elif args.command == 'export-schemas':
            export_schemas()
            return 0
            
        else:
            print(f"Unknown command: {args.command}")
            return 1
            
    except Exception as e:
        print(f"❌ Error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == '__main__':
    sys.exit(main())
