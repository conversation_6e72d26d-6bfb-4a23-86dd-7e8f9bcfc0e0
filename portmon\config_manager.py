# File: portmon/config_manager.py
# Description: Improved configuration with Pydantic schema validation, configurable timeouts, and smarter rate limiting

import yaml
import logging
import os
import json
from typing import Optional, Dict, List, Union
from pathlib import Path
from enum import Enum
from pydantic import BaseModel, Field, validator, root_validator, ValidationError

logger = logging.getLogger(__name__)

# Default config file path (can be overridden via environment variable)
CONFIG_FILE = os.environ.get('PORTMON_CONFIG_FILE', 'config.yaml')
DAILY_SCAN_TRACKER_FILE = Path.cwd() / '.portmon_last_daily_scan.dat'

# Cache for configuration to improve performance
_cached_config = None
_cached_config_mtime = None

class NotificationMode(str, Enum):
    """Valid notification modes for non-critical ports."""
    IMMEDIATE = "immediate"
    DIGEST = "digest"

def validate_port(port_value: Union[str, int]) -> int:
    """Validate a port value and convert to integer."""
    try:
        port_num = int(port_value)
        if 1 <= port_num <= 65535:
            return port_num
        raise ValueError(f"Port {port_value} out of valid range (1-65535)")
    except ValueError as e:
        if "out of valid range" in str(e):
            raise
        raise ValueError(f"Invalid port format: {port_value}")

class PortmonConfig(BaseModel):
    """Schema for Port Monitor configuration."""
    # Core settings
    targets: List[str] = Field(..., min_items=1, description="List of target domains to monitor")
    recon_base_dir: Path = Field(..., description="Base directory for reconnaissance data")
    scan_interval_seconds: int = Field(7200, ge=1, description="How often to run regular scan cycle (seconds)")
    max_concurrent_targets: int = Field(5, ge=1, description="How many targets to scan in parallel")
    
    @validator('recon_base_dir')
    def must_be_dir(cls, v):
        """Validate that recon_base_dir exists and is a directory."""
        p = Path(v) if not isinstance(v, Path) else v
        if not p.exists():
            raise ValueError(f"recon_base_dir does not exist: {v}")
        if not p.is_dir():
            raise ValueError(f"recon_base_dir must be a directory, not a file: {v}")
        return p
    
    # Tool paths
    naabu_path: str = Field("naabu", description="Path to naabu binary")
    httpx_path: str = Field("httpx", description="Path to httpx binary")
    nmap_path: str = Field("nmap", description="Path to nmap binary")
    
    # Tool performance settings
    naabu_rate: int = Field(0, ge=0, description="Packets per second (0 = naabu default)")
    naabu_concurrency: int = Field(100, ge=1, description="Concurrent host scans")
    naabu_flags: List[str] = Field(
        [
            '-silent',    # Silent output (no unnecessary info)
        ],
        description="Flags to pass to naabu command (excluding -l, -o, -c, -rate which are managed internally)"
    )
    httpx_threads: int = Field(100, ge=1, description="Concurrent probes")
    probe_timeout: int = Field(15, ge=1, description="Httpx probe timeout")
    
    # Timeout settings (new)
    naabu_timeout_seconds: int = Field(5400, ge=1, description="Timeout for Naabu scans in seconds")
    httpx_process_timeout_seconds: int = Field(300, ge=1, description="Timeout for Httpx process in seconds")
    nmap_timeout_seconds: int = Field(300, ge=1, description="Timeout for Nmap scans in seconds")
    
    # Rate limiting (new)
    global_scan_rate_limit: Optional[int] = Field(None, ge=0, 
        description="Global limit on concurrent scans. When set, restricts the maximum number of concurrent scans regardless of max_concurrent_targets.")
    
    # Port settings
    top_ports: int = Field(1000, ge=1, description="Used if custom_ports is empty")
    custom_ports: Union[List[int], str] = Field([], description="Specific ports to scan (overrides top_ports)")
    exclude_ports: Union[List[int], str] = Field([], description="Always exclude these ports")
    common_https_ports: List[int] = Field([], description="Ports to try HTTPS first before HTTP")
    critical_ports: Union[List[int], str] = Field([], description="Critical ports triggering immediate alerts")
    
    # Service fingerprinting
    nmap_scan_enabled: bool = Field(True, description="Enable Nmap service fingerprinting")
    nmap_flags: List[str] = Field(
        [
            '-sV',          # Service/version detection
            '-sC',          # Default scripts
            '--open',       # Only show ports found to be open
            '-T4',          # Aggressive timing template
            '-Pn',          # Skip host discovery
        ],
        description="Flags to pass to nmap command (excluding -p, --host-timeout, and -oX which are managed internally)"
    )
    
    # Cleanup settings
    cleanup_temp_files: bool = Field(False, description="Automatically delete temporary scan files after each cycle")
    
    # Notification settings
    discord_webhook_url: str = Field(..., description="Discord webhook URL for notifications")
    notification_mode: NotificationMode = Field(NotificationMode.DIGEST, description="'immediate' or 'digest' for non-critical ports")
    discord_retry_attempts: int = Field(3, ge=1, description="Number of retry attempts for Discord notifications")
    discord_retry_delay: int = Field(5, ge=1, description="Initial delay in seconds between Discord notification retries")
    
    # Custom validators
    @validator('custom_ports', 'exclude_ports', 'critical_ports', 'common_https_ports', pre=True)
    def normalize_port_lists(cls, v):
        """Normalize port lists from strings or lists to validated port integers."""
        # Handle string input (comma-separated)
        if isinstance(v, str):
            if not v.strip():
                return []
            ports = [p.strip() for p in v.split(',')]
        # Handle list input
        elif isinstance(v, list):
            ports = [str(p).strip() for p in v]
        else:
            return []
            
        # Validate port numbers and convert to integers
        valid_ports = []
        for port in ports:
            try:
                valid_ports.append(validate_port(port))
            except ValueError as e:
                logger.warning(f"{e}")
                
        return valid_ports
        
    @validator('discord_webhook_url')
    def validate_discord_webhook(cls, v):
        """Basic validation for Discord webhook URL format."""
        if not v.startswith('https://discord.com/api/webhooks/'):
            logger.warning(f"Discord webhook URL '{v}' may be incorrect (doesn't match expected format)")
        return v
        
    @root_validator(skip_on_failure=True)
    def check_port_config(cls, values):
        """Validate port configuration as a whole."""
        if not values.get('custom_ports') and values.get('top_ports', 0) <= 0:
            raise ValueError("Either 'custom_ports' must be specified or 'top_ports' must be positive")
        return values

def load_config(force_reload: bool = False) -> Optional[Dict[str, Union[str, int, bool, list, dict]]]:
    """
    Load and validate YAML config using Pydantic schema.
    
    Args:
        force_reload: If True, reload config even if cached
        
    Returns:
        Dict with validated configuration or None on error
    """
    global _cached_config, _cached_config_mtime
    
    # Use cached config if available and not forced to reload
    config_path = Path(CONFIG_FILE)
    if not force_reload and _cached_config is not None and config_path.exists():
        try:
            current_mtime = config_path.stat().st_mtime
            if current_mtime == _cached_config_mtime:
                logger.debug("Using cached configuration")
                return _cached_config
        except OSError:
            # If we can't stat the file, ignore and proceed with reload
            pass
    
    try:
        # Load raw YAML
        with open(CONFIG_FILE, 'r') as f:
            raw_config = yaml.safe_load(f)
        
        if not raw_config:
            logger.error(f"Config file {CONFIG_FILE} is empty or invalid")
            return None
            
        logger.info(f"Loaded config from {CONFIG_FILE}")
        
        # Check for environment variable overrides
        env_config = os.environ.get('PORTMON_CONFIG_JSON')
        if env_config:
            try:
                env_overrides = json.loads(env_config)
                logger.info(f"Applying configuration overrides from PORTMON_CONFIG_JSON environment variable")
                raw_config.update(env_overrides)
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse PORTMON_CONFIG_JSON: {e}")
        
        # Validate with Pydantic
        try:
            config = PortmonConfig(**raw_config)
            # Convert to dict for backward compatibility
            config_dict = config.dict()
            
            # Log important configuration details
            logger.info(f"Configured targets: {len(config.targets)}")
            
            if config.critical_ports:
                logger.info(f"Critical ports triggering immediate alerts: {config.critical_ports}")
            else:
                logger.info("No critical ports defined; all alerts follow 'notification_mode'")
                
            if config.nmap_scan_enabled:
                logger.info(f"Nmap scanning for new ports is ENABLED (Timeout: {config.nmap_timeout_seconds}s)")
            else:
                logger.info("Nmap scanning for new ports is DISABLED")
                
            logger.info(f"Default notification mode for non-critical ports: {config.notification_mode}")
            
            if config.naabu_rate > 0:
                logger.info(f"Naabu rate limit set to: {config.naabu_rate} pps")
            else:
                logger.info("Using Naabu default rate limit")
                
            if config.global_scan_rate_limit:
                logger.info(f"Global scan rate limit enabled: {config.global_scan_rate_limit} concurrent scans")
                
            logger.debug(f"Final configuration: {config_dict}")
            
            # Update cache
            if config_path.exists():
                _cached_config = config_dict
                _cached_config_mtime = config_path.stat().st_mtime
                
            return config_dict
            
        except ValidationError as ve:
            logger.error(f"Configuration validation failed:\n{ve.json()}")
            return None
            
        except Exception as e:
            logger.error(f"Unexpected error during configuration processing: {e}")
            return None
            
    except FileNotFoundError:
        logger.error(f"Config file not found: {CONFIG_FILE}")
    except yaml.YAMLError as e:
        logger.error(f"YAML error parsing {CONFIG_FILE}: {e}")
    except Exception as e:
        logger.error(f"Unexpected error loading config: {e}", exc_info=True)
        
    return None

def get_last_daily_scan_date() -> Optional[str]:
    """Get the date of the last daily scan from tracker file."""
    try:
        if DAILY_SCAN_TRACKER_FILE.exists():
            return DAILY_SCAN_TRACKER_FILE.read_text().strip()
    except Exception as e:
        logger.error(f"Error reading daily scan tracker file {DAILY_SCAN_TRACKER_FILE}: {e}")
    return None

def update_last_daily_scan_date(date_str: str) -> None:
    """Update the date of the last daily scan in tracker file."""
    try:
        DAILY_SCAN_TRACKER_FILE.write_text(date_str)
        logger.info(f"Updated daily scan tracker file ({DAILY_SCAN_TRACKER_FILE}) to: {date_str}")
    except Exception as e:
        logger.error(f"Error updating daily scan tracker file: {e}")