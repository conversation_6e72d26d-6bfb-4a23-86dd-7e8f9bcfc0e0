# Notifications package
from .notifier import (
    send_discord_notification,
    send_digest_notification,
    get_notification_manager,
    NotificationSeverity,
    NotificationChannel,
    NotificationMetrics,
    HttpProbeResult,
    NmapResult,
    NmapScriptOutput
)
from .http_utils import (
    send_post,
    close_session,
    get_session,
    get_http_metrics,
    reset_http_metrics,
    HttpMetrics
)

__all__ = [
    # Legacy notification functions
    'send_discord_notification',
    'send_digest_notification',
    # New notification system
    'get_notification_manager',
    'NotificationSeverity',
    'NotificationChannel',
    'NotificationMetrics',
    # Data classes
    'HttpProbeResult',
    'NmapResult',
    'NmapScriptOutput',
    # HTTP utilities
    'send_post',
    'close_session',
    'get_session',
    'get_http_metrics',
    'reset_http_metrics',
    'HttpMetrics'
]
