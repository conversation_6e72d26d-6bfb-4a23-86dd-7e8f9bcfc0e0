# File: portmon/scanner.py
# Description: Refactored to use the centralized command_runner utility.

import time
import logging
from pathlib import Path
from typing import Set, Dict, Any, Optional
from colorama import Fore, Style, init as colorama_init

# Import the centralized command utility
from .command_runner import run_command, parse_line_based_output

colorama_init()
logger = logging.getLogger(__name__)

def run_naabu_scan(config: Dict[str, Any],
                   paths: Dict[str, Path],
                   target: str,
                   scan_mode: str = 'regular') -> Optional[Set[str]]:
    """
    Runs a Naabu port scan against the subdomains file for a given target.

    Args:
        config: The global configuration dictionary.
        paths: Dict with keys 'subdomains_file' and 'scan_output_dir'.
        target: The target identifier (e.g., 'example.com').
        scan_mode: 'regular' or 'daily_top_1000'. Determines port settings.

    Returns:
        A set of "host:port" strings discovered, or None on critical failure.
    """
    discovered_ports: Set[str] = set()
    timestamp = int(time.time())
    safe_name = "".join(c if c.isalnum() else "_" for c in target)
    subdomains_file = paths['subdomains_file']
    output_file = paths['scan_output_dir'] / f"naabu_{safe_name}_{scan_mode}_{timestamp}.txt"
    naabu_path = config.get('naabu_path', 'naabu') # Use path from config if set

    if not subdomains_file.is_file() or subdomains_file.stat().st_size == 0:
        logger.error(f"{Fore.RED}[-] Subdomains file missing/empty: {subdomains_file}. Cannot scan {target}.{Style.RESET_ALL}")
        return None

    # Base command + Global Concurrency Flag
    naabu_concurrency = str(config.get('naabu_concurrency', 100))
    cmd = [
        naabu_path,
        '-l', str(subdomains_file),
        '-o', str(output_file),
        '-c', naabu_concurrency
    ]
    
    # Add custom naabu flags from config
    naabu_flags = config.get('naabu_flags', ['-silent'])
    if naabu_flags:
        # Add each flag individually
        for flag in naabu_flags:
            # Skip any already added flags to avoid duplicates
            if flag not in ['-l', '-o', '-c', '-rate', '-p', '-top-ports', '-exclude-ports']:
                cmd.append(flag)
        logger.debug(f"Using custom naabu flags: {naabu_flags}")
    else:
        # Fallback to silent flag if none specified
        cmd.append('-silent')

    # --- ADDED: Naabu Rate Limiting ---
    naabu_rate = config.get('naabu_rate', 0)
    if naabu_rate > 0:
        cmd.extend(['-rate', str(naabu_rate)])
        logger.info(f"Naabu [{scan_mode}] using rate limit: {naabu_rate} pps for {target}")
    # ---------------------------------

    # Port settings based on mode
    exclude_ports = config.get('exclude_ports', [])
    if exclude_ports:
        excl = ",".join(map(str, exclude_ports))  # Convert integers to strings
        cmd.extend(['-exclude-ports', excl])
        # Logging moved slightly down

    scan_type_log = f"{scan_mode.replace('_', ' ').title()}" # Nicer log message
    port_setting_log = "" # Build port setting log message

    if scan_mode == 'daily_top_1000':
        cmd.extend(['-top-ports', '1000'])
        port_setting_log = "using forced top 1000 ports"
    else: # 'regular' scan mode
        custom_ports  = config.get('custom_ports', [])
        top_ports_config = config.get('top_ports', 0)

        if custom_ports:
            pts = ",".join(map(str, custom_ports))  # Convert integers to strings
            cmd.extend(['-p', pts])
            port_setting_log = f"using custom ports ({len(custom_ports)} specified)"
            if top_ports_config > 0:
                logger.debug(f"Naabu [{scan_mode}] Note: Configured top_ports={top_ports_config} ignored for {target} because custom_ports is specified.")
        elif top_ports_config > 0:
            cmd.extend(['-top-ports', str(top_ports_config)])
            port_setting_log = f"using configured top {top_ports_config} ports"
        else:
            port_setting_log = "using Naabu default ports"

    # Log combined info
    logger.info(
        f"{Fore.YELLOW}[*] Running Naabu {scan_type_log} scan for {target} "
        f"({port_setting_log}"
        f"{f', excluding {len(exclude_ports)}' if exclude_ports else ''}"
        f"{f', rate {naabu_rate} pps' if naabu_rate > 0 else ''}"
        f"), output → {output_file.name}{Style.RESET_ALL}"
    )
    logger.debug(f"Naabu command: {' '.join(cmd)}")

    # Get timeout from config
    process_timeout = config.get('naabu_timeout_seconds', 5400)
    
    # Use the centralized command runner
    success, _, _, _ = run_command(
        cmd=cmd,
        timeout=process_timeout,
        description=f"Naabu [{scan_mode}] scan for {target}",
        output_file=output_file,
        max_output_length=1000,
        stream_output=False  # Set to True for very large scans
    )
    
    if not success:
        logger.warning(f"{Fore.YELLOW}[!] Naabu scan may have encountered issues for {target}{Style.RESET_ALL}")
        # Continue to check for output file anyway, as it might have partial results

    # Parse output using the centralized parser
    if output_file.exists():
        try:
            # Parse with centralized utility
            lines = parse_line_based_output(output_file=output_file)
            discovered_ports = {line for line in lines if ':' in line}
            logger.info(f"{Fore.GREEN}[+] Found {len(discovered_ports)} ports from {scan_type_log} scan for {target}.{Style.RESET_ALL}")
        except Exception as e:
            logger.error(f"{Fore.RED}[-] Error processing Naabu output {output_file}: {e}{Style.RESET_ALL}", exc_info=True)
            return set()  # Return empty set on parse error
    else:
        logger.warning(f"{Fore.YELLOW}[!] Naabu output file missing for {target} ({scan_type_log} scan){Style.RESET_ALL}")
        return set()  # Return empty set if file is missing

    return discovered_ports